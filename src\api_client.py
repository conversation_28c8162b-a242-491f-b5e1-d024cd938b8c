"""
API客户端 - 通过Selenium浏览器获取服务台数据
"""
import logging
import json
import time
from typing import List, Dict, Any, Optional
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from .models import Issue, Attachment, Comment


class ServiceDeskAPIClient:
    """服务台API客户端 - 基于Selenium"""

    def __init__(self, driver, base_url: str):
        self.driver = driver
        self.base_url = base_url
        self.wait = WebDriverWait(self.driver, 10)
        self.logger = logging.getLogger(__name__)

    def get_all_requests(self) -> List[Dict[str, Any]]:
        """通过Selenium获取所有的服务请求"""
        try:
            url = f"{self.base_url}/request/"
            self.logger.info(f"通过浏览器访问API: {url}")

            # 使用Selenium访问API地址
            self.driver.get(url)
            time.sleep(3)  # 等待页面加载

            # 检查是否被重定向到登录页面
            current_url = self.driver.current_url
            if 'login' in current_url.lower():
                self.logger.error(f"API访问被重定向到登录页面: {current_url}")
                self.logger.error("可能的原因: 会话已过期或认证失败")
                return []

            # 获取页面源码（JSON数据）
            page_source = self.driver.page_source

            # 检查是否包含认证错误信息
            if 'Client must be authenticated' in page_source:
                self.logger.error("API返回认证错误: Client must be authenticated")
                self.logger.error("当前页面内容预览:")
                self.logger.error(page_source[:500] + "...")
                return []

            # 从页面源码中提取JSON数据
            json_data = self._extract_json_from_page(page_source)
            if not json_data:
                self.logger.error("无法从页面中提取JSON数据")
                self.logger.debug(f"页面内容预览: {page_source[:200]}...")
                return []

            values = json_data.get('values', [])
            self.logger.info(f"成功获取 {len(values)} 个服务请求")
            return values

        except Exception as e:
            self.logger.error(f"获取服务请求失败: {e}")
            return []

    def get_issue_attachments(self, self_url: str) -> List[Attachment]:
        """通过Selenium获取指定issue的附件"""
        try:
            attachment_url = f"{self_url}/attachment"
            self.logger.debug(f"通过浏览器获取附件: {attachment_url}")

            # 使用Selenium访问附件API地址
            self.driver.get(attachment_url)
            time.sleep(1)  # 等待页面加载

            # 获取页面源码并解析JSON
            page_source = self.driver.page_source
            json_data = self._extract_json_from_page(page_source)

            if not json_data:
                self.logger.debug("附件API返回空数据")
                return []

            attachments = []
            for item in json_data.get('values', []):
                attachment = Attachment(
                    filename=item.get('filename', ''),
                    content_url=item.get('_links', {}).get('content', ''),
                    issue_id=self._extract_issue_id_from_url(self_url)
                )
                attachments.append(attachment)

            self.logger.debug(f"获取到 {len(attachments)} 个附件")
            return attachments

        except Exception as e:
            self.logger.error(f"获取附件失败: {e}")
            return []

    def get_issue_comments(self, self_url: str) -> List[Comment]:
        """通过Selenium获取指定issue的回复"""
        try:
            comment_url = f"{self_url}/comment"
            self.logger.debug(f"通过浏览器获取回复: {comment_url}")

            # 使用Selenium访问回复API地址
            self.driver.get(comment_url)
            time.sleep(1)  # 等待页面加载

            # 获取页面源码并解析JSON
            page_source = self.driver.page_source
            json_data = self._extract_json_from_page(page_source)

            if not json_data:
                self.logger.debug("回复API返回空数据")
                return []

            comments = []
            for item in json_data.get('values', []):
                comment = Comment(
                    id=str(item.get('id', '')),
                    body=item.get('body', ''),
                    author_name=item.get('author', {}).get('displayName', ''),
                    created_time=item.get('created', {}).get('epochMillis', 0),
                    issue_id=self._extract_issue_id_from_url(self_url)
                )
                comments.append(comment)

            self.logger.debug(f"获取到 {len(comments)} 个回复")
            return comments

        except Exception as e:
            self.logger.error(f"获取回复失败: {e}")
            return []

    def _extract_issue_id_from_url(self, self_url: str) -> str:
        """从self URL中提取issue ID"""
        try:
            # URL格式通常是: .../request/{issue_id}
            return self_url.split('/')[-1]
        except:
            return ""

    def download_attachment(self, content_url: str, filename: str, save_path: str) -> bool:
        """通过Selenium下载附件"""
        try:
            self.logger.debug(f"通过浏览器下载附件: {filename}")

            # 使用Selenium访问附件下载链接
            self.driver.get(content_url)
            time.sleep(2)  # 等待下载开始

            # 注意：实际的文件下载会根据浏览器设置自动保存到下载目录
            # 这里我们只是触发下载，文件会保存到配置的下载目录
            self.logger.debug(f"附件下载已触发: {filename}")
            return True

        except Exception as e:
            self.logger.error(f"下载附件失败 {filename}: {e}")
            return False

    def build_issue_from_data(self, issue_data: Dict[str, Any]) -> Issue:
        """从API数据构建Issue对象"""
        issue_id = str(issue_data.get('issueId', ''))
        self_url = issue_data.get('_links', {}).get('self', '')

        # 获取附件和回复
        attachments = self.get_issue_attachments(self_url)
        comments = self.get_issue_comments(self_url)

        issue = Issue(
            issue_id=issue_id,
            self_url=self_url,
            raw_data=issue_data,
            attachments=attachments,
            comments=comments
        )

        return issue

    def _extract_json_from_page(self, page_source: str) -> Optional[Dict[str, Any]]:
        """从页面源码中提取JSON数据"""
        try:
            # 方法1: 查找<pre>标签中的JSON数据
            import re

            # 查找<pre>标签内容
            pre_match = re.search(r'<pre[^>]*>(.*?)</pre>', page_source, re.DOTALL | re.IGNORECASE)
            if pre_match:
                json_text = pre_match.group(1).strip()
                # 解码HTML实体
                import html
                json_text = html.unescape(json_text)
                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    pass

            # 方法2: 查找页面中的JSON对象
            # 寻找以{开头，}结尾的JSON数据
            json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            json_matches = re.findall(json_pattern, page_source, re.DOTALL)

            for match in json_matches:
                try:
                    data = json.loads(match)
                    # 检查是否包含values字段（API响应的特征）
                    if isinstance(data, dict) and 'values' in data:
                        return data
                except json.JSONDecodeError:
                    continue

            # 方法3: 查找整个页面的JSON内容（如果页面就是纯JSON）
            try:
                # 移除HTML标签，只保留内容
                clean_text = re.sub(r'<[^>]+>', '', page_source).strip()
                if clean_text.startswith('{') and clean_text.endswith('}'):
                    return json.loads(clean_text)
            except json.JSONDecodeError:
                pass

            self.logger.debug("无法从页面中提取有效的JSON数据")
            return None

        except Exception as e:
            self.logger.error(f"提取JSON数据时发生错误: {e}")
            return None
