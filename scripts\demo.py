#!/usr/bin/env python3
"""
演示脚本 - 展示如何使用服务台数据采集工具
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.data_collector import ServiceDeskDataCollector


def demo_dry_run():
    """演示运行（不实际执行登录）"""
    print("🎯 服务台数据采集工具演示")
    print("=" * 50)

    # 1. 创建采集器
    print("1️⃣ 创建数据采集器...")
    collector = ServiceDeskDataCollector()

    # 2. 加载配置
    print("2️⃣ 加载配置文件...")
    if not collector.load_config():
        print("❌ 配置加载失败")
        return False

    print(f"✅ 配置加载成功")
    print(f"   📧 邮箱: {collector.config.email}")
    print(f"   🏢 组织: {collector.config.domain}")
    print(f"   🌐 门户: {collector.config.urls.get('portal')}")

    # 3. 检查配置
    print("\n3️⃣ 检查配置...")
    if collector.config.email == "<EMAIL>":
        print("⚠️  请在 config.yaml 中设置正确的邮箱")
        return False

    if collector.config.password == "your-password":
        print("⚠️  请在 config.yaml 中设置正确的密码")
        return False

    if collector.config.domain == "your-organization":
        print("⚠️  请在 config.yaml 中设置正确的组织域名")
        return False

    print("✅ 配置检查通过")

    # 4. 显示将要执行的步骤
    print("\n4️⃣ 执行计划:")
    print("   🔧 初始化Selenium WebDriver")
    print("   🌐 访问服务台门户")
    print("   🔐 自动登录")
    print("   🔗 获取会话cookies")
    print("   📡 调用API获取所有服务请求")
    print("   📎 获取每个请求的附件信息")
    print("   💬 获取每个请求的回复信息")
    print("   💾 保存数据到JSON和CSV文件")
    print("   📁 下载附件文件（如果启用）")

    print("\n✅ 演示完成！")
    print("🚀 要开始实际采集，请运行: python main.py")

    return True


def show_help():
    """显示帮助信息"""
    print("📖 服务台数据采集工具使用指南")
    print("=" * 50)
    print()
    print("🔧 配置步骤:")
    print("1. 复制 config.example.yaml 为 config.yaml")
    print("2. 编辑 config.yaml，设置你的:")
    print("   - 邮箱地址")
    print("   - 密码")
    print("   - 组织域名（例如：tuduweb）")
    print()
    print("🚀 运行命令:")
    print("python main.py              # 开始数据采集")
    print("python test_config.py       # 测试配置文件")
    print("python demo.py              # 查看演示")
    print("python demo.py --help       # 显示帮助")
    print()
    print("📁 输出文件:")
    print("output/servicedesk_data_*.json    # 完整数据（JSON格式）")
    print("output/issues_*.csv               # Issues列表（CSV格式）")
    print("output/comments_*.csv             # 回复数据（CSV格式）")
    print("output/attachments/               # 附件文件")
    print()
    print("📋 日志文件:")
    print("servicedesk_collector.log         # 详细操作日志")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_help()
    else:
        demo_dry_run()
