#!/usr/bin/env python3
"""
项目设置脚本
"""
import os
import shutil
import subprocess
import sys


def setup_project():
    """设置项目"""
    print("🚀 开始设置服务台数据采集项目...")

    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_root)

    # 1. 创建必要的目录
    directories = ['data', 'data/output', 'data/output/attachments', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")

    # 2. 复制配置文件示例
    if not os.path.exists('config.yaml'):
        if os.path.exists('config.template.yaml'):
            shutil.copy('config.template.yaml', 'config.yaml')
            print("📋 已创建配置文件: config.yaml")
            print("⚠️  请编辑 config.yaml 文件，设置你的邮箱、密码和组织域名")
        elif os.path.exists('config.example.yaml'):
            shutil.copy('config.example.yaml', 'config.yaml')
            print("📋 已创建配置文件: config.yaml")
            print("⚠️  请编辑 config.yaml 文件，设置你的邮箱、密码和组织域名")
        else:
            print("❌ 找不到配置文件模板")
    else:
        print("📋 配置文件已存在: config.yaml")

    # 3. 安装依赖
    print("📦 安装Python依赖...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

    # 4. 测试配置
    print("🔧 测试配置...")
    try:
        from src.models import Config
        import yaml

        with open('config.yaml', 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)
        print("✅ 配置文件格式正确")

    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

    print("\n🎉 项目设置完成！")
    print("\n📝 下一步:")
    print("1. 编辑 config.yaml 文件，设置你的登录信息")
    print("2. 运行: python tests/test_config.py 测试配置")
    print("3. 运行: python main.py 开始数据采集")

    return True


if __name__ == "__main__":
    setup_project()
