# 开发者指南

## 开发环境设置

### 1. 克隆项目
```bash
git clone <repository-url>
cd selenium-servicedesk
```

### 2. 创建虚拟环境（推荐）
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 3. 安装开发依赖
```bash
pip install -r requirements.txt
pip install pytest pytest-cov black flake8  # 开发工具
```

### 4. 配置开发环境
```bash
# 复制配置模板
cp config.template.yaml config.yaml

# 编辑配置文件
nano config.yaml
```

## 代码结构

### 核心模块

#### `src/models.py`
- 数据模型定义
- 配置管理
- 数据序列化

#### `src/selenium_handler.py`
- 浏览器自动化
- 登录流程
- 会话管理

#### `src/api_client.py`
- API数据获取
- JSON解析
- 错误处理

#### `src/data_collector.py`
- 主要业务逻辑
- 流程控制
- 数据输出

## 开发工作流

### 1. 功能开发
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发代码
# ...

# 运行测试
python -m pytest tests/

# 代码格式化
black src/ tests/

# 提交代码
git add .
git commit -m "feat: add new feature"
```

### 2. 测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python tests/test_config.py

# 生成覆盖率报告
python -m pytest tests/ --cov=src --cov-report=html
```

### 3. 调试
```bash
# 启用详细日志
python main.py --verbose

# 运行诊断工具
python scripts/diagnose_auth_issue.py

# 单步调试
python -m pdb main.py
```

## 添加新功能

### 1. 添加新的数据模型
```python
# 在 src/models.py 中添加
@dataclass
class NewModel:
    field1: str
    field2: int
```

### 2. 扩展API客户端
```python
# 在 src/api_client.py 中添加
def get_new_data(self, url: str) -> List[Dict]:
    """获取新类型的数据"""
    # 实现逻辑
    pass
```

### 3. 添加测试
```python
# 在 tests/ 中创建测试文件
def test_new_feature():
    """测试新功能"""
    # 测试逻辑
    assert True
```

## 代码规范

### Python代码风格
- 使用 Black 进行代码格式化
- 遵循 PEP 8 规范
- 使用类型提示

### 文档规范
- 所有公共方法必须有文档字符串
- 使用 Markdown 格式编写文档
- 保持文档与代码同步

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式化
refactor: 重构
test: 测试相关
chore: 构建/工具相关
```

## 测试策略

### 单元测试
- 测试各个模块的独立功能
- 使用 mock 模拟外部依赖
- 保持测试的独立性

### 集成测试
- 测试模块间的交互
- 测试完整的数据采集流程
- 使用真实的配置进行测试

### 端到端测试
- 测试完整的用户场景
- 包括登录、数据采集、输出
- 在不同环境中验证

## 性能优化

### 1. 浏览器优化
```python
# 使用无头模式
selenium:
  headless: true

# 禁用图片加载
chrome_options.add_argument('--disable-images')

# 设置页面加载策略
chrome_options.add_argument('--page-load-strategy=eager')
```

### 2. 数据处理优化
```python
# 批量处理数据
def process_batch(items, batch_size=100):
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]

# 使用生成器减少内存使用
def get_issues():
    for issue in issues:
        yield process_issue(issue)
```

### 3. 并发处理
```python
# 使用线程池处理附件下载
from concurrent.futures import ThreadPoolExecutor

with ThreadPoolExecutor(max_workers=5) as executor:
    futures = [executor.submit(download_attachment, url) 
               for url in attachment_urls]
```

## 错误处理

### 1. 异常分类
- `ConfigError`: 配置相关错误
- `AuthenticationError`: 认证失败
- `APIError`: API访问错误
- `DataError`: 数据处理错误

### 2. 错误恢复
```python
def robust_api_call(url, max_retries=3):
    for attempt in range(max_retries):
        try:
            return make_api_call(url)
        except APIError as e:
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # 指数退避
```

### 3. 日志记录
```python
# 使用结构化日志
logger.info("API call started", extra={
    'url': url,
    'attempt': attempt,
    'user_id': user_id
})
```

## 部署和发布

### 1. 版本管理
```bash
# 更新版本号
git tag v1.0.0

# 创建发布分支
git checkout -b release/v1.0.0
```

### 2. 打包
```bash
# 创建分发包
python setup.py sdist bdist_wheel

# 检查包
twine check dist/*
```

### 3. 文档更新
- 更新 CHANGELOG.md
- 更新 README.md
- 更新 API 文档

## 贡献指南

### 1. 报告问题
- 使用 GitHub Issues
- 提供详细的错误信息
- 包含复现步骤

### 2. 提交代码
- Fork 项目
- 创建功能分支
- 提交 Pull Request
- 通过代码审查

### 3. 代码审查
- 检查代码质量
- 验证测试覆盖率
- 确保文档完整

## 工具和资源

### 开发工具
- **IDE**: VS Code, PyCharm
- **调试**: pdb, VS Code debugger
- **测试**: pytest, coverage
- **格式化**: black, isort
- **静态分析**: flake8, mypy

### 有用的资源
- [Selenium 文档](https://selenium-python.readthedocs.io/)
- [Atlassian API 文档](https://developer.atlassian.com/cloud/jira/service-desk/rest/)
- [Python 最佳实践](https://docs.python-guide.org/)

## 常见开发问题

### 1. Selenium 版本兼容性
```bash
# 检查版本
pip list | grep selenium

# 更新到兼容版本
pip install selenium==4.15.2
```

### 2. ChromeDriver 问题
```bash
# 自动管理 ChromeDriver
pip install webdriver-manager

# 手动下载
# https://chromedriver.chromium.org/
```

### 3. 依赖冲突
```bash
# 使用虚拟环境
python -m venv venv

# 锁定依赖版本
pip freeze > requirements.lock
```
