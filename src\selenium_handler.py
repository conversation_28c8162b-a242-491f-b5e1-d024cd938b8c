"""
Selenium自动化处理类
"""
import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .models import Config


class SeleniumHandler:
    """Selenium操作处理类"""

    def __init__(self, config: Config):
        self.config = config
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)

    def setup_driver(self):
        """设置WebDriver"""
        try:
            # Chrome选项配置
            chrome_options = Options()

            if self.config.selenium.get('headless', False):
                chrome_options.add_argument('--headless')

            # 获取Chrome路径配置
            chrome_paths = self.config.selenium.get('chrome_paths', {})

            # 设置Chrome可执行文件路径
            executable_path = chrome_paths.get('executable_path', '')
            if executable_path:
                # 处理路径格式，确保使用正确的分隔符
                executable_path = self._normalize_path(executable_path)
                chrome_options.binary_location = executable_path
                self.logger.info(f"使用指定的Chrome路径: {executable_path}")

            # 设置用户数据目录
            user_data_dir = chrome_paths.get('user_data_dir', '')
            if user_data_dir:
                # 处理路径格式并创建目录
                user_data_dir = self._normalize_path(user_data_dir)
                self._ensure_directory_exists(user_data_dir)
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                self.logger.info(f"使用指定的用户数据目录: {user_data_dir}")

            # 设置下载目录
            download_dir = chrome_paths.get('download_dir', '')
            if download_dir:
                # 处理路径格式并创建目录
                download_dir = self._normalize_path(download_dir)
                self._ensure_directory_exists(download_dir)
                prefs = {
                    "download.default_directory": os.path.abspath(download_dir),
                    "download.prompt_for_download": False,
                    "download.directory_upgrade": True,
                    "safebrowsing.enabled": True
                }
                chrome_options.add_experimental_option("prefs", prefs)
                self.logger.info(f"使用指定的下载目录: {download_dir}")

            # 其他Chrome选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # 创建WebDriver Service
            driver_path = chrome_paths.get('driver_path', '')
            if driver_path:
                # 处理路径格式
                driver_path = self._normalize_path(driver_path)
                service = Service(driver_path)
                self.logger.info(f"使用指定的ChromeDriver路径: {driver_path}")
            else:
                service = Service(ChromeDriverManager().install())
                self.logger.info("使用自动下载的ChromeDriver")

            # 创建WebDriver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # 设置超时时间
            timeouts = self.config.selenium.get('timeouts', {})
            self.driver.implicitly_wait(timeouts.get('implicit_wait', 10))
            self.driver.set_page_load_timeout(timeouts.get('page_load_timeout', 30))
            self.driver.set_script_timeout(timeouts.get('script_timeout', 30))

            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, 10)

            self.logger.info("WebDriver设置成功")
            return True

        except Exception as e:
            self.logger.error(f"WebDriver设置失败: {e}")
            return False

    def login(self) -> bool:
        """执行登录流程"""
        try:
            # 1. 访问服务台门户
            portal_url = self.config.urls.get('portal')
            self.logger.info(f"访问服务台门户: {portal_url}")
            self.driver.get(portal_url)

            # 等待页面加载
            time.sleep(3)

            # 2. 检查是否需要登录（可能直接跳转到登录页面）
            current_url = self.driver.current_url
            if 'login' not in current_url:
                # 尝试查找登录按钮或链接
                try:
                    login_link = self.wait.until(
                        EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "Log in"))
                    )
                    login_link.click()
                    time.sleep(2)
                except TimeoutException:
                    self.logger.info("未找到登录链接，可能已经登录或页面结构不同")

            # 3. 输入邮箱
            email_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "user-email"))
            )
            email_input.clear()
            email_input.send_keys(self.config.email)
            self.logger.info("邮箱输入完成")

            # 4. 点击下一步
            next_button = self.wait.until(
                EC.element_to_be_clickable((By.ID, "login-button"))
            )
            next_button.click()
            self.logger.info("点击下一步")

            # 5. 等待密码输入框出现并输入密码
            password_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "user-password"))
            )
            password_input.clear()
            password_input.send_keys(self.config.password)
            self.logger.info("密码输入完成")

            # 6. 点击Continue登录
            continue_button = self.wait.until(
                EC.element_to_be_clickable((By.ID, "login-button"))
            )
            continue_button.click()
            self.logger.info("点击Continue登录")

            # 7. 验证登录成功
            return self._verify_login_success()

        except TimeoutException as e:
            self.logger.error(f"登录超时: {e}")
            return False
        except Exception as e:
            self.logger.error(f"登录过程中发生错误: {e}")
            return False

    def _verify_login_success(self) -> bool:
        """验证登录是否成功"""
        try:
            # 等待页面跳转并查找"Welcome to the Help Center"文本
            self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome to the Help Center')]"))
            )
            self.logger.info("登录成功：找到'Welcome to the Help Center'文本")

            # 额外验证：尝试访问服务台主页确保会话有效
            return self._verify_session_validity()

        except TimeoutException:
            # 如果没有找到欢迎文本，检查URL是否包含servicedesk
            current_url = self.driver.current_url
            if 'servicedesk' in current_url and 'login' not in current_url:
                self.logger.info("登录成功：URL验证通过")
                return self._verify_session_validity()
            else:
                self.logger.error(f"登录失败：当前URL {current_url}")
                return False

    def _verify_session_validity(self) -> bool:
        """验证会话有效性"""
        try:
            # 访问服务台主页验证会话
            servicedesk_url = f"https://{self.config.domain}.atlassian.net/servicedesk"
            self.logger.info(f"验证会话有效性：访问 {servicedesk_url}")

            self.driver.get(servicedesk_url)
            time.sleep(3)  # 等待页面加载

            current_url = self.driver.current_url
            if 'login' in current_url:
                self.logger.error("会话验证失败：被重定向到登录页面")
                return False

            # 检查重要的cookies
            cookies = self.get_cookies()
            important_cookies = ['JSESSIONID', 'atlassian.xsrf.token']
            missing_cookies = [cookie for cookie in important_cookies if cookie not in cookies]

            if missing_cookies:
                self.logger.warning(f"缺少重要cookies: {missing_cookies}")

            self.logger.info(f"会话验证成功：当前URL {current_url}")
            self.logger.info(f"获得 {len(cookies)} 个cookies")
            return True

        except Exception as e:
            self.logger.error(f"会话验证过程中发生错误: {e}")
            return False

    def prepare_api_session(self) -> bool:
        """准备API访问会话"""
        try:
            self.logger.info("准备API访问会话...")

            # 1. 访问服务台主页建立上下文
            servicedesk_main_url = f"https://{self.config.domain}.atlassian.net/servicedesk"
            self.logger.info(f"访问服务台主页: {servicedesk_main_url}")
            self.driver.get(servicedesk_main_url)
            time.sleep(3)

            # 检查是否成功访问
            current_url = self.driver.current_url
            if 'login' in current_url:
                self.logger.error("访问服务台主页被重定向到登录页面")
                return False

            # 2. 尝试访问API基础URL
            api_base_url = self.config.urls.get('api_base')
            self.logger.info(f"测试API基础访问: {api_base_url}")
            self.driver.get(api_base_url)
            time.sleep(2)

            # 检查API基础访问
            current_url = self.driver.current_url
            page_source = self.driver.page_source

            if 'login' in current_url:
                self.logger.error("API基础访问被重定向到登录页面")
                return False

            if 'Client must be authenticated' in page_source:
                self.logger.error("API基础访问返回认证错误")
                # 尝试替代方案：访问用户信息API
                return self._try_alternative_api_access()

            self.logger.info("API会话准备成功")
            return True

        except Exception as e:
            self.logger.error(f"准备API会话时发生错误: {e}")
            return False

    def _try_alternative_api_access(self) -> bool:
        """尝试替代的API访问方式"""
        try:
            self.logger.info("尝试替代API访问方式...")

            # 尝试访问用户信息API
            user_api_url = f"https://{self.config.domain}.atlassian.net/rest/api/2/myself"
            self.logger.info(f"测试用户API: {user_api_url}")
            self.driver.get(user_api_url)
            time.sleep(2)

            page_source = self.driver.page_source
            if 'accountId' in page_source or 'displayName' in page_source:
                self.logger.info("用户API访问成功，会话有效")
                return True

            # 尝试访问服务台信息API
            servicedesk_info_url = f"https://{self.config.domain}.atlassian.net/rest/servicedeskapi/info"
            self.logger.info(f"测试服务台信息API: {servicedesk_info_url}")
            self.driver.get(servicedesk_info_url)
            time.sleep(2)

            page_source = self.driver.page_source
            if 'version' in page_source and 'Client must be authenticated' not in page_source:
                self.logger.info("服务台信息API访问成功")
                return True

            self.logger.warning("所有替代API访问方式都失败了")
            return False

        except Exception as e:
            self.logger.error(f"替代API访问尝试失败: {e}")
            return False

    def get_cookies(self) -> dict:
        """获取当前会话的cookies"""
        if self.driver:
            cookies = {}
            for cookie in self.driver.get_cookies():
                cookies[cookie['name']] = cookie['value']
            return cookies
        return {}

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.logger.info("浏览器已关闭")

    def _normalize_path(self, path: str) -> str:
        """标准化路径格式"""
        if not path:
            return path

        # 将反斜杠替换为正斜杠（Windows兼容）
        normalized = path.replace('\\', '/')

        # 展开用户目录（~）
        if normalized.startswith('~'):
            normalized = os.path.expanduser(normalized)

        # 处理相对路径
        if not os.path.isabs(normalized):
            normalized = os.path.abspath(normalized)

        return normalized

    def _ensure_directory_exists(self, directory: str) -> bool:
        """确保目录存在，如果不存在则创建"""
        try:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                self.logger.info(f"创建目录: {directory}")
            return True
        except Exception as e:
            self.logger.error(f"创建目录失败 {directory}: {e}")
            return False
