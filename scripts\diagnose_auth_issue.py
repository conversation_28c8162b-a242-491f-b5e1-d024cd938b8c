#!/usr/bin/env python3
"""
诊断认证问题的脚本
"""
import sys
import os
import time
import yaml

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config
from src.selenium_handler import SeleniumHandler


def diagnose_authentication():
    """诊断认证问题"""
    print("🔍 诊断认证问题...")
    print("=" * 50)

    try:
        # 1. 加载配置
        print("1️⃣ 加载配置...")
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)
        print(f"   📧 邮箱: {config.email}")
        print(f"   🏢 组织: {config.domain}")
        print(f"   🌐 门户URL: {config.urls.get('portal')}")
        print(f"   🔗 API URL: {config.urls.get('api_base')}")

        # 2. 初始化Selenium
        print("\n2️⃣ 初始化Selenium...")
        selenium_handler = SeleniumHandler(config)
        if not selenium_handler.setup_driver():
            print("   ❌ Selenium初始化失败")
            return False
        print("   ✅ Selenium初始化成功")

        # 3. 执行登录
        print("\n3️⃣ 执行登录...")
        if not selenium_handler.login():
            print("   ❌ 登录失败")
            selenium_handler.close()
            return False
        print("   ✅ 登录成功")

        # 4. 检查当前URL和cookies
        print("\n4️⃣ 检查登录状态...")
        current_url = selenium_handler.driver.current_url
        print(f"   🌐 当前URL: {current_url}")

        cookies = selenium_handler.get_cookies()
        print(f"   🍪 Cookies数量: {len(cookies)}")

        # 显示重要的cookies
        important_cookies = ['JSESSIONID', 'atlassian.xsrf.token', 'cloud.session.token']
        for cookie_name in important_cookies:
            if cookie_name in cookies:
                print(f"   ✅ 找到Cookie: {cookie_name}")
            else:
                print(f"   ⚠️  缺少Cookie: {cookie_name}")

        # 5. 测试API访问
        print("\n5️⃣ 测试API访问...")
        api_base_url = config.urls.get('api_base')
        api_request_url = f"{api_base_url}/request/"

        print(f"   📡 访问API: {api_request_url}")
        selenium_handler.driver.get(api_request_url)
        time.sleep(3)  # 等待页面加载

        # 检查页面内容
        page_source = selenium_handler.driver.page_source
        current_url_after_api = selenium_handler.driver.current_url

        print(f"   🌐 API访问后URL: {current_url_after_api}")

        # 检查是否被重定向到登录页面
        if 'login' in current_url_after_api.lower():
            print("   ❌ 被重定向到登录页面 - 认证失败")
            print("   💡 可能原因:")
            print("      - 登录域名与API域名不匹配")
            print("      - 会话已过期")
            print("      - Cookie作用域问题")
        elif 'Client must be authenticated' in page_source:
            print("   ❌ 页面显示认证错误")
            print("   💡 可能原因:")
            print("      - API访问权限不足")
            print("      - 需要特定的API权限")
        elif 'values' in page_source or '{' in page_source:
            print("   ✅ API访问成功 - 获取到数据")
        else:
            print("   ⚠️  API响应异常")
            print(f"   📄 页面内容预览: {page_source[:200]}...")

        # 6. 域名一致性检查
        print("\n6️⃣ 域名一致性检查...")
        portal_domain = config.urls.get('portal', '').split('//')[1].split('/')[0] if '//' in config.urls.get('portal', '') else ''
        api_domain = config.urls.get('api_base', '').split('//')[1].split('/')[0] if '//' in config.urls.get('api_base', '') else ''

        print(f"   🌐 门户域名: {portal_domain}")
        print(f"   🔗 API域名: {api_domain}")

        if portal_domain == api_domain:
            print("   ✅ 域名一致")
        else:
            print("   ❌ 域名不一致 - 这可能是问题所在")

        # 7. 尝试直接访问服务台主页
        print("\n7️⃣ 测试服务台主页访问...")
        servicedesk_url = f"https://{config.domain}.atlassian.net/servicedesk"
        selenium_handler.driver.get(servicedesk_url)
        time.sleep(2)

        servicedesk_page_url = selenium_handler.driver.current_url
        print(f"   🌐 服务台页面URL: {servicedesk_page_url}")

        if 'login' in servicedesk_page_url:
            print("   ❌ 服务台页面也需要重新登录")
        else:
            print("   ✅ 服务台页面访问正常")

        # 8. 建议解决方案
        print("\n8️⃣ 建议解决方案...")
        suggestions = [
            "1. 确保登录后立即访问API，减少等待时间",
            "2. 检查配置文件中的域名设置是否正确",
            "3. 尝试在登录后先访问服务台主页，再访问API",
            "4. 检查账号是否有API访问权限",
            "5. 考虑在API访问前添加额外的等待时间"
        ]

        for suggestion in suggestions:
            print(f"   💡 {suggestion}")

        selenium_handler.close()
        return True

    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        return False


def test_step_by_step_access():
    """逐步测试访问流程"""
    print("\n🔬 逐步测试访问流程...")
    print("=" * 50)

    try:
        # 加载配置
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = Config(config_dict)

        # 初始化Selenium
        selenium_handler = SeleniumHandler(config)
        selenium_handler.setup_driver()

        # 登录
        selenium_handler.login()

        # 逐步访问不同的页面
        test_urls = [
            ("服务台主页", f"https://{config.domain}.atlassian.net/servicedesk"),
            ("客户门户", config.urls.get('portal')),
            ("API基础", config.urls.get('api_base')),
            ("请求API", f"{config.urls.get('api_base')}/request/")
        ]

        for name, url in test_urls:
            print(f"\n🔗 测试访问: {name}")
            print(f"   URL: {url}")

            selenium_handler.driver.get(url)
            time.sleep(2)

            current_url = selenium_handler.driver.current_url
            page_title = selenium_handler.driver.title

            print(f"   📍 实际URL: {current_url}")
            print(f"   📄 页面标题: {page_title}")

            if 'login' in current_url.lower():
                print("   ❌ 被重定向到登录页面")
            elif 'error' in page_title.lower() or 'Client must be authenticated' in selenium_handler.driver.page_source:
                print("   ❌ 访问被拒绝")
            else:
                print("   ✅ 访问成功")

        selenium_handler.close()

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    print("🚀 认证问题诊断工具")
    print("=" * 50)

    # 运行诊断
    diagnose_authentication()

    # 逐步测试
    test_step_by_step_access()

    print("\n✅ 诊断完成！")
    print("\n📋 如果问题仍然存在，请检查:")
    print("1. 账号是否有API访问权限")
    print("2. 组织是否启用了API访问")
    print("3. 是否需要特殊的认证头或参数")
