# 项目结构说明

## 目录结构

```
selenium-servicedesk/
├── 📁 src/                          # 源代码目录
│   ├── __init__.py                  # 包初始化文件
│   ├── models.py                    # 数据模型定义
│   ├── selenium_handler.py          # Selenium自动化操作
│   ├── api_client.py               # API客户端
│   └── data_collector.py           # 主要采集逻辑
├── 📁 tests/                        # 测试文件目录
│   ├── test_config.py              # 配置测试
│   ├── test_chrome_paths.py        # Chrome路径测试
│   ├── test_selenium_api.py        # Selenium API测试
│   └── test_auth_fix.py            # 认证修复测试
├── 📁 scripts/                      # 脚本工具目录
│   ├── setup.py                    # 项目设置脚本
│   ├── demo.py                     # 完整演示脚本
│   ├── simple_demo.py              # 简化演示脚本
│   ├── diagnose_auth_issue.py      # 认证问题诊断
│   └── fix_auth_issue.py           # 认证修复方案
├── 📁 docs/                         # 文档目录
│   ├── README.md                   # 项目说明
│   ├── PROJECT_SUMMARY.md          # 项目总结
│   ├── PROJECT_STRUCTURE.md        # 项目结构说明（本文件）
│   ├── PATH_CONFIG_GUIDE.md        # Chrome路径配置指南
│   └── SELENIUM_API_IMPLEMENTATION.md # Selenium API实现说明
├── 📁 data/                         # 数据目录（被git忽略）
│   ├── output/                     # 输出数据
│   │   ├── attachments/            # 附件文件
│   │   ├── *.json                  # JSON数据文件
│   │   └── *.csv                   # CSV数据文件
│   └── downloads/                  # 浏览器下载目录
├── 📁 logs/                         # 日志目录（被git忽略）
│   └── *.log                       # 日志文件
├── 📄 main.py                       # 主程序入口
├── 📄 requirements.txt              # Python依赖
├── 📄 config.template.yaml          # 配置文件模板
├── 📄 config.example.yaml           # 配置文件示例
├── 📄 .gitignore                    # Git忽略文件
└── 📄 LICENSE                       # 许可证文件
```

## 文件说明

### 核心源代码 (`src/`)

- **`models.py`**: 数据模型定义
  - `Attachment`: 附件模型
  - `Comment`: 回复模型
  - `Issue`: 服务请求模型
  - `ServiceDeskData`: 数据集合模型
  - `Config`: 配置模型

- **`selenium_handler.py`**: Selenium自动化操作
  - 浏览器初始化和配置
  - 自动登录流程
  - 会话验证和API准备
  - Chrome路径处理

- **`api_client.py`**: API客户端
  - 基于Selenium的API访问
  - JSON数据提取
  - 附件和回复获取

- **`data_collector.py`**: 主要采集逻辑
  - 整合所有功能模块
  - 数据采集流程控制
  - 多格式数据输出

### 测试文件 (`tests/`)

- **`test_config.py`**: 配置文件验证
- **`test_chrome_paths.py`**: Chrome路径配置测试
- **`test_selenium_api.py`**: Selenium API访问测试
- **`test_auth_fix.py`**: 认证问题修复测试

### 工具脚本 (`scripts/`)

- **`setup.py`**: 项目初始化和依赖安装
- **`demo.py`**: 完整功能演示
- **`simple_demo.py`**: 简化演示（无需依赖）
- **`diagnose_auth_issue.py`**: 认证问题诊断工具
- **`fix_auth_issue.py`**: 认证修复方案说明

### 文档 (`docs/`)

- **`README.md`**: 项目主要说明文档
- **`PROJECT_SUMMARY.md`**: 项目功能和技术总结
- **`PATH_CONFIG_GUIDE.md`**: Chrome路径配置详细指南
- **`SELENIUM_API_IMPLEMENTATION.md`**: Selenium API实现技术说明

## 配置文件管理

### 敏感信息保护

1. **`config.template.yaml`**: 版本控制中的模板文件
   - 包含所有配置项的结构
   - 使用占位符代替敏感信息
   - 提供配置说明和示例

2. **`config.yaml`**: 本地实际配置文件
   - 包含真实的邮箱、密码等敏感信息
   - 被`.gitignore`忽略，不会上传到版本控制

3. **`config.example.yaml`**: 配置示例文件
   - 提供详细的配置说明
   - 包含各种配置选项的示例

### 配置文件使用流程

1. 复制模板文件：
   ```bash
   cp config.template.yaml config.yaml
   ```

2. 编辑本地配置：
   ```bash
   # 修改 config.yaml 中的敏感信息
   nano config.yaml
   ```

3. 测试配置：
   ```bash
   python tests/test_config.py
   ```

## 数据目录结构

### 输出数据 (`data/output/`)

- **JSON文件**: `servicedesk_data_YYYYMMDD_HHMMSS.json`
  - 完整的结构化数据
  - 包含所有Issues、附件、回复信息

- **CSV文件**: 
  - `issues_YYYYMMDD_HHMMSS.csv`: Issues列表
  - `comments_YYYYMMDD_HHMMSS.csv`: 所有回复数据

- **附件目录**: `attachments/`
  - 格式: `{issue_id}_{filename}`
  - 自动下载的附件文件

### 日志文件 (`logs/`)

- **`servicedesk_collector.log`**: 主要操作日志
- **`selenium.log`**: Selenium操作日志
- **`api.log`**: API访问日志

## Git版本控制

### 被忽略的文件/目录

- 敏感配置文件 (`config.yaml`)
- 数据和输出文件 (`data/`, `output/`)
- 日志文件 (`logs/`, `*.log`)
- Python缓存 (`__pycache__/`)
- 临时文件和Chrome数据

### 版本控制的文件

- 源代码 (`src/`)
- 测试文件 (`tests/`)
- 工具脚本 (`scripts/`)
- 文档 (`docs/`)
- 配置模板 (`config.template.yaml`)
- 项目配置 (`requirements.txt`, `.gitignore`)

## 使用建议

### 开发环境设置

1. 克隆项目后首先运行：
   ```bash
   python scripts/setup.py
   ```

2. 配置敏感信息：
   ```bash
   cp config.template.yaml config.yaml
   # 编辑 config.yaml
   ```

3. 运行测试：
   ```bash
   python tests/test_config.py
   ```

### 数据采集

1. 完整采集：
   ```bash
   python main.py
   ```

2. 测试采集：
   ```bash
   python scripts/simple_demo.py
   ```

### 问题诊断

1. 配置问题：
   ```bash
   python tests/test_config.py
   ```

2. 认证问题：
   ```bash
   python scripts/diagnose_auth_issue.py
   ```

3. Chrome路径问题：
   ```bash
   python tests/test_chrome_paths.py
   ```
