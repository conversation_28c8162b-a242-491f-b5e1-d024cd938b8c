# 快速开始指南

## 🚀 5分钟快速上手

### 1. 克隆项目
```bash
git clone <repository-url>
cd selenium-servicedesk
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置项目
```bash
# 复制配置模板
cp config.template.yaml config.yaml

# 编辑配置文件
nano config.yaml
```

**必须修改的配置项**：
```yaml
servicedesk:
  base_domain: "your-organization"  # 你的Atlassian组织名
  login:
    email: "<EMAIL>"     # 你的邮箱
    password: "your-password"           # 你的密码
```

### 4. 测试配置
```bash
python tests/test_config.py
```

### 5. 运行数据采集
```bash
python main.py
```

## 📁 输出文件位置

采集完成后，数据将保存在：
- `data/output/servicedesk_data_*.json` - 完整数据
- `data/output/issues_*.csv` - Issues列表
- `data/output/comments_*.csv` - 回复数据
- `data/output/attachments/` - 附件文件

## 🔧 常见问题

### 问题1: 登录失败
```bash
# 运行诊断工具
python scripts/diagnose_auth_issue.py
```

### 问题2: Chrome路径问题
```bash
# 测试Chrome配置
python tests/test_chrome_paths.py
```

### 问题3: API访问失败
```bash
# 测试API访问
python tests/test_selenium_api.py
```

## 📖 更多信息

- [完整文档](docs/README.md)
- [项目结构](docs/PROJECT_STRUCTURE.md)
- [技术实现](docs/SELENIUM_API_IMPLEMENTATION.md)
- [Chrome配置](docs/PATH_CONFIG_GUIDE.md)
