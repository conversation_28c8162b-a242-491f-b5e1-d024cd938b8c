# 服务台配置模板文件
# 复制此文件为 config.yaml 并填入真实信息

servicedesk:
  # 基础配置 - 修改为你的组织名
  base_domain: "your-organization"
  
  # 登录配置
  login:
    email: "<EMAIL>"  # 请修改为实际邮箱
    password: "your-password"        # 请修改为实际密码
  
  # URL配置（通常不需要修改）
  urls:
    portal: "https://{domain}.atlassian.net/servicedesk/customer/portals"
    login: "https://{domain}.atlassian.net/login"
    api_base: "https://{domain}.atlassian.net/rest/servicedeskapi"
    requests_api: "https://{domain}.atlassian.net/rest/servicedeskapi/request/"

# Selenium配置
selenium:
  # 浏览器配置
  browser: "chrome"  # chrome, firefox, edge
  headless: false    # 设置为true可以无头模式运行
  
  # Chrome路径配置
  chrome_paths:
    # Chrome可执行文件路径（可选，留空则自动检测）
    executable_path: ""
    
    # 用户数据目录（可选，留空则使用临时目录）
    user_data_dir: ""
    
    # 下载目录（可选）
    download_dir: "./data/downloads"
    
    # ChromeDriver路径（可选，留空则自动下载）
    driver_path: ""
  
  # 等待时间配置
  timeouts:
    implicit_wait: 10
    page_load_timeout: 30
    script_timeout: 30
  
  # 元素定位器（通常不需要修改）
  locators:
    email_input: "user-email"
    password_input: "user-password"
    login_button: "login-button"
    welcome_text: "Welcome to the Help Center"

# 数据采集配置
data_collection:
  # 输出配置
  output_dir: "data/output"
  
  # 文件格式
  formats:
    - "json"
    - "csv"
  
  # 是否下载附件
  download_attachments: true
  attachments_dir: "data/output/attachments"
