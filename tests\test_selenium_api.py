#!/usr/bin/env python3
"""
测试基于Selenium的API访问
"""
import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config
from src.selenium_handler import SeleniumHandler
from src.api_client import ServiceDeskAPIClient


def test_json_extraction():
    """测试JSON提取功能"""
    print("🧪 测试JSON提取功能...")

    # 模拟不同格式的页面内容
    test_cases = [
        # 情况1: <pre>标签包含的JSON
        {
            'name': 'PRE标签JSON',
            'content': '''
            <html>
            <body>
            <pre>{"values": [{"issueId": "123", "summary": "测试问题"}], "total": 1}</pre>
            </body>
            </html>
            ''',
            'expected': True
        },

        # 情况2: 纯JSON页面
        {
            'name': '纯JSON页面',
            'content': '{"values": [{"issueId": "456", "summary": "另一个问题"}], "total": 1}',
            'expected': True
        },

        # 情况3: 包含HTML实体的JSON
        {
            'name': 'HTML实体JSON',
            'content': '<pre>{"values": [{"issueId": "789", "summary": "&quot;带引号的问题&quot;"}], "total": 1}</pre>',
            'expected': True
        },

        # 情况4: 无效内容
        {
            'name': '无效内容',
            'content': '<html><body>这不是JSON</body></html>',
            'expected': False
        }
    ]

    # 创建临时的API客户端来测试
    from src.models import Config
    config = Config({})

    # 模拟driver（实际测试中不需要真实的driver）
    class MockDriver:
        pass

    api_client = ServiceDeskAPIClient(MockDriver(), "http://test.com")

    for test_case in test_cases:
        print(f"  测试: {test_case['name']}")
        try:
            result = api_client._extract_json_from_page(test_case['content'])

            if test_case['expected']:
                if result and 'values' in result:
                    print(f"    ✅ 成功提取JSON: {len(result['values'])} 个项目")
                else:
                    print(f"    ❌ 期望提取成功但失败了")
            else:
                if result is None:
                    print(f"    ✅ 正确识别为无效内容")
                else:
                    print(f"    ❌ 期望失败但提取成功了")

        except Exception as e:
            print(f"    ❌ 测试出错: {e}")


def test_api_url_construction():
    """测试API URL构建"""
    print("\n🔗 测试API URL构建...")

    try:
        # 加载配置
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            import yaml
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)

        # 显示API URLs
        print(f"  📡 API基础URL: {config.urls.get('api_base')}")
        print(f"  📋 请求API: {config.urls.get('requests_api')}")

        # 模拟构建具体的API URLs
        base_url = config.urls.get('api_base')
        if base_url:
            requests_url = f"{base_url}/request/"
            print(f"  🔍 服务请求URL: {requests_url}")

            # 模拟issue的self URL
            sample_issue_url = f"{base_url}/request/12345"
            attachment_url = f"{sample_issue_url}/attachment"
            comment_url = f"{sample_issue_url}/comment"

            print(f"  📎 附件URL示例: {attachment_url}")
            print(f"  💬 回复URL示例: {comment_url}")

        return True

    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False


def demo_selenium_api_flow():
    """演示Selenium API访问流程"""
    print("\n🎯 Selenium API访问流程演示")
    print("=" * 50)

    print("📋 基于Selenium的API访问流程:")
    steps = [
        "1. 🔧 初始化Selenium WebDriver",
        "2. 🌐 访问服务台门户并登录",
        "3. 🍪 获得有效的登录会话",
        "4. 📡 使用WebDriver访问API URL:",
        "   - driver.get('https://domain.atlassian.net/rest/servicedeskapi/request/')",
        "5. 📄 获取页面源码 (page_source)",
        "6. 🔍 从页面源码中提取JSON数据:",
        "   - 查找<pre>标签中的JSON",
        "   - 解析HTML实体编码",
        "   - 验证JSON格式",
        "7. 📊 解析values数组获取所有issues",
        "8. 🔄 对每个issue重复步骤4-7:",
        "   - 访问 {issue_url}/attachment",
        "   - 访问 {issue_url}/comment",
        "9. 💾 整合所有数据并保存"
    ]

    for step in steps:
        print(f"  {step}")

    print("\n✨ 优势:")
    advantages = [
        "✅ 无需处理复杂的认证和会话管理",
        "✅ 自动继承浏览器的登录状态",
        "✅ 支持所有浏览器支持的认证方式",
        "✅ 可以处理JavaScript渲染的内容",
        "✅ 更接近真实用户的访问方式"
    ]

    for advantage in advantages:
        print(f"  {advantage}")


def show_implementation_details():
    """显示实现细节"""
    print("\n🔧 实现细节")
    print("=" * 30)

    print("📝 关键代码示例:")

    code_examples = [
        {
            'title': '访问API URL',
            'code': '''
# 使用Selenium访问API
self.driver.get(f"{self.base_url}/request/")
time.sleep(2)  # 等待页面加载
page_source = self.driver.page_source
'''
        },
        {
            'title': '提取JSON数据',
            'code': '''
# 从<pre>标签提取JSON
import re
pre_match = re.search(r'<pre[^>]*>(.*?)</pre>', page_source, re.DOTALL)
if pre_match:
    json_text = html.unescape(pre_match.group(1).strip())
    return json.loads(json_text)
'''
        },
        {
            'title': '处理附件和回复',
            'code': '''
# 获取附件
self.driver.get(f"{issue_url}/attachment")
attachment_data = self._extract_json_from_page(self.driver.page_source)

# 获取回复
self.driver.get(f"{issue_url}/comment")
comment_data = self._extract_json_from_page(self.driver.page_source)
'''
        }
    ]

    for example in code_examples:
        print(f"\n📌 {example['title']}:")
        print(example['code'])


def main():
    """主函数"""
    print("🚀 基于Selenium的API访问测试工具")
    print("=" * 50)

    # 1. 测试JSON提取
    test_json_extraction()

    # 2. 测试URL构建
    test_api_url_construction()

    # 3. 演示流程
    demo_selenium_api_flow()

    # 4. 显示实现细节
    show_implementation_details()

    print("\n✅ 测试完成！")
    print("\n📖 说明:")
    print("  - 此方法通过Selenium浏览器访问API地址")
    print("  - 自动继承登录会话，无需额外认证")
    print("  - 从页面源码中提取JSON数据")
    print("  - 支持所有类型的API响应格式")


if __name__ == "__main__":
    main()
