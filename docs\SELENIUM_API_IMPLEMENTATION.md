# 基于Selenium的API访问实现

## 概述

本项目采用**纯Selenium浏览器方案**来访问Atlassian服务台API，完全通过浏览器自动化来获取数据，无需额外的API认证处理。

## 技术架构

### 传统方式 vs Selenium方式

| 方面 | 传统API方式 | Selenium方式 |
|------|-------------|--------------|
| 认证 | 需要API Token/OAuth | 自动继承浏览器登录状态 |
| 会话管理 | 手动处理cookies/headers | 浏览器自动管理 |
| 请求方式 | requests.get() | driver.get() |
| 数据获取 | response.json() | 从page_source提取JSON |
| 复杂度 | 高（需要处理认证） | 低（浏览器处理一切） |

## 实现细节

### 1. 登录阶段
```python
# 使用Selenium自动登录
driver.get("https://domain.atlassian.net/servicedesk/customer/portals")
# 输入邮箱、密码，完成登录
# 浏览器自动保存登录状态
```

### 2. API访问阶段
```python
# 直接访问API地址
driver.get("https://domain.atlassian.net/rest/servicedeskapi/request/")

# 获取页面源码
page_source = driver.page_source

# 从页面中提取JSON数据
json_data = extract_json_from_page(page_source)
```

### 3. JSON数据提取

支持多种JSON格式的提取：

#### 方法1: `<pre>`标签包含的JSON
```html
<html>
<body>
<pre>{"values": [...], "total": 10}</pre>
</body>
</html>
```

#### 方法2: 纯JSON页面
```json
{"values": [...], "total": 10}
```

#### 方法3: HTML实体编码的JSON
```html
<pre>{"summary": "&quot;带引号的内容&quot;"}</pre>
```

### 4. 数据提取算法

```python
def _extract_json_from_page(self, page_source: str) -> Optional[Dict[str, Any]]:
    # 1. 查找<pre>标签中的JSON
    pre_match = re.search(r'<pre[^>]*>(.*?)</pre>', page_source, re.DOTALL)
    if pre_match:
        json_text = html.unescape(pre_match.group(1).strip())
        return json.loads(json_text)
    
    # 2. 查找页面中的JSON对象
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    for match in re.findall(json_pattern, page_source):
        try:
            data = json.loads(match)
            if isinstance(data, dict) and 'values' in data:
                return data
        except json.JSONDecodeError:
            continue
    
    # 3. 处理纯JSON页面
    clean_text = re.sub(r'<[^>]+>', '', page_source).strip()
    if clean_text.startswith('{') and clean_text.endswith('}'):
        return json.loads(clean_text)
```

## 完整数据采集流程

### 1. 初始化
```python
# 设置Chrome浏览器
selenium_handler = SeleniumHandler(config)
selenium_handler.setup_driver()

# 执行登录
selenium_handler.login()

# 初始化API客户端（传入WebDriver）
api_client = ServiceDeskAPIClient(selenium_handler.driver, api_base_url)
```

### 2. 获取所有服务请求
```python
# 访问请求列表API
driver.get("https://domain.atlassian.net/rest/servicedeskapi/request/")
requests_data = api_client.get_all_requests()
```

### 3. 获取每个请求的详细信息
```python
for request_data in requests_data:
    issue_id = request_data.get('issueId')
    self_url = request_data.get('_links', {}).get('self')
    
    # 获取附件
    driver.get(f"{self_url}/attachment")
    attachments = api_client.get_issue_attachments(self_url)
    
    # 获取回复
    driver.get(f"{self_url}/comment")
    comments = api_client.get_issue_comments(self_url)
```

## 优势分析

### ✅ 技术优势

1. **无需API认证**
   - 不需要申请API Token
   - 不需要处理OAuth流程
   - 自动继承浏览器登录状态

2. **简化开发**
   - 减少认证相关代码
   - 降低配置复杂度
   - 更容易调试和维护

3. **兼容性强**
   - 支持所有浏览器支持的认证方式
   - 包括双因素认证、SSO等
   - 适应不同的企业环境

4. **真实用户体验**
   - 模拟真实用户操作
   - 更不容易被反爬虫检测
   - 可以处理JavaScript渲染的内容

### ⚠️ 注意事项

1. **性能考虑**
   - 浏览器启动需要时间
   - 页面加载比纯API请求慢
   - 内存占用相对较高

2. **稳定性**
   - 依赖浏览器稳定性
   - 需要处理页面加载超时
   - 可能受到页面结构变化影响

## 配置示例

### 基础配置
```yaml
selenium:
  browser: "chrome"
  headless: false  # 可设为true进行后台运行
  
  chrome_paths:
    executable_path: ""     # 留空自动检测
    user_data_dir: ""      # 留空使用临时目录
    download_dir: "./output/downloads"
    driver_path: ""        # 留空自动下载
```

### 高级配置
```yaml
selenium:
  browser: "chrome"
  headless: true  # 无头模式，适合服务器环境
  
  chrome_paths:
    executable_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"
    user_data_dir: "./chrome_user_data"
    download_dir: "./output/downloads"
    driver_path: "./drivers/chromedriver.exe"
  
  timeouts:
    implicit_wait: 10
    page_load_timeout: 30
    script_timeout: 30
```

## 测试和验证

### 1. 测试JSON提取功能
```bash
python test_selenium_api.py
```

### 2. 测试Chrome路径配置
```bash
python test_chrome_paths.py
```

### 3. 测试完整流程
```bash
python simple_demo.py
```

## 故障排除

### 常见问题

1. **JSON提取失败**
   - 检查API响应格式
   - 确认页面完全加载
   - 增加等待时间

2. **登录失败**
   - 检查账号密码
   - 确认网络连接
   - 检查页面元素定位

3. **浏览器启动失败**
   - 检查Chrome安装
   - 更新ChromeDriver
   - 检查路径配置

## 总结

基于Selenium的API访问方案提供了一种简单、可靠的数据采集方式，特别适合需要处理复杂认证的场景。虽然在性能上不如纯API调用，但在易用性和兼容性方面具有显著优势。

这种方案特别适合：
- 企业内部数据采集
- 需要处理复杂认证的场景
- 快速原型开发
- 不便申请API权限的情况
