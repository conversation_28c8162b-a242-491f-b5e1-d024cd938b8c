#!/usr/bin/env python3
"""
修复认证问题的解决方案
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)


def add_session_preparation_to_selenium_handler():
    """为SeleniumHandler添加会话准备方法"""

    additional_method = '''
    def prepare_api_session(self) -> bool:
        """准备API访问会话"""
        try:
            self.logger.info("准备API访问会话...")

            # 1. 访问服务台主页建立上下文
            servicedesk_main_url = f"https://{self.config.domain}.atlassian.net/servicedesk"
            self.logger.info(f"访问服务台主页: {servicedesk_main_url}")
            self.driver.get(servicedesk_main_url)
            time.sleep(2)

            # 检查是否成功访问
            current_url = self.driver.current_url
            if 'login' in current_url:
                self.logger.error("访问服务台主页被重定向到登录页面")
                return False

            # 2. 尝试访问API基础URL
            api_base_url = self.config.urls.get('api_base')
            self.logger.info(f"测试API基础访问: {api_base_url}")
            self.driver.get(api_base_url)
            time.sleep(2)

            # 检查API基础访问
            current_url = self.driver.current_url
            page_source = self.driver.page_source

            if 'login' in current_url:
                self.logger.error("API基础访问被重定向到登录页面")
                return False

            if 'Client must be authenticated' in page_source:
                self.logger.error("API基础访问返回认证错误")
                return False

            self.logger.info("API会话准备成功")
            return True

        except Exception as e:
            self.logger.error(f"准备API会话时发生错误: {e}")
            return False
'''

    print("🔧 建议在SeleniumHandler类中添加以下方法:")
    print(additional_method)


def suggest_data_collector_improvements():
    """建议数据采集器的改进"""

    improved_flow = '''
def run(self) -> bool:
    """运行完整的数据采集流程"""
    try:
        # 1. 加载配置
        if not self.load_config():
            return False

        # 2. 初始化Selenium
        if not self.initialize_selenium():
            return False

        # 3. 执行登录
        if not self.login():
            return False

        # 4. 准备API会话 (新增步骤)
        if not self.selenium_handler.prepare_api_session():
            self.logger.error("API会话准备失败")
            return False

        # 5. 初始化API客户端
        if not self.initialize_api_client():
            return False

        # 6. 采集数据
        data = self.collect_data()
        if not data:
            return False

        # 7. 保存数据
        if not self.save_data(data):
            return False

        self.logger.info("数据采集流程完成")
        return True

    except Exception as e:
        self.logger.error(f"数据采集流程失败: {e}")
        return False

    finally:
        # 清理资源
        if self.selenium_handler:
            self.selenium_handler.close()
'''

    print("\n🔄 建议的数据采集流程改进:")
    print(improved_flow)


def suggest_alternative_approach():
    """建议替代方案"""

    print("\n💡 替代解决方案:")
    print("=" * 50)

    solutions = [
        {
            'title': '方案1: 使用不同的API端点',
            'description': '尝试使用内部API或不同的端点',
            'steps': [
                '1. 尝试访问 /rest/api/2/search 而不是 servicedeskapi',
                '2. 使用 /rest/servicedeskapi/servicedesk 获取服务台信息',
                '3. 通过服务台ID构建正确的API路径'
            ]
        },
        {
            'title': '方案2: 模拟用户界面操作',
            'description': '通过UI界面获取数据而不是直接访问API',
            'steps': [
                '1. 登录后访问服务台客户门户',
                '2. 通过UI界面浏览所有请求',
                '3. 从页面HTML中提取数据'
            ]
        },
        {
            'title': '方案3: 使用代理模式',
            'description': '通过浏览器代理捕获API请求',
            'steps': [
                '1. 设置浏览器代理',
                '2. 在UI中触发数据加载',
                '3. 从代理中捕获API响应'
            ]
        }
    ]

    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['title']}")
        print(f"   描述: {solution['description']}")
        print("   步骤:")
        for step in solution['steps']:
            print(f"   {step}")


def create_debug_version():
    """创建调试版本的建议"""

    debug_code = '''
def debug_api_access(self):
    """调试API访问问题"""
    try:
        # 记录当前状态
        self.logger.info("=== API访问调试信息 ===")
        self.logger.info(f"当前URL: {self.driver.current_url}")
        self.logger.info(f"页面标题: {self.driver.title}")

        # 记录cookies
        cookies = self.get_cookies()
        self.logger.info(f"Cookies数量: {len(cookies)}")
        for name, value in cookies.items():
            self.logger.info(f"Cookie: {name} = {value[:50]}...")

        # 尝试不同的API端点
        test_endpoints = [
            f"https://{self.config.domain}.atlassian.net/rest/api/2/myself",
            f"https://{self.config.domain}.atlassian.net/rest/servicedeskapi/servicedesk",
            f"https://{self.config.domain}.atlassian.net/rest/servicedeskapi/info"
        ]

        for endpoint in test_endpoints:
            self.logger.info(f"测试端点: {endpoint}")
            self.driver.get(endpoint)
            time.sleep(2)

            current_url = self.driver.current_url
            page_source = self.driver.page_source[:500]

            self.logger.info(f"  结果URL: {current_url}")
            self.logger.info(f"  页面内容: {page_source}")

            if 'login' in current_url:
                self.logger.info("  状态: 需要登录")
            elif 'Client must be authenticated' in page_source:
                self.logger.info("  状态: 认证失败")
            elif '{' in page_source:
                self.logger.info("  状态: 成功获取数据")
            else:
                self.logger.info("  状态: 未知响应")

    except Exception as e:
        self.logger.error(f"调试过程中发生错误: {e}")
'''

    print("\n🐛 建议添加调试方法:")
    print(debug_code)


def main():
    """主函数"""
    print("🚀 认证问题修复方案")
    print("=" * 50)

    print("📋 问题分析:")
    print("'Client must be authenticated' 错误通常由以下原因造成:")
    print("1. 登录域名与API域名不匹配")
    print("2. 会话cookies没有正确传递到API域名")
    print("3. 需要特定的API访问权限")
    print("4. API端点需要额外的认证头")

    # 提供解决方案
    add_session_preparation_to_selenium_handler()
    suggest_data_collector_improvements()
    suggest_alternative_approach()
    create_debug_version()

    print("\n✅ 建议的修复步骤:")
    print("1. 运行诊断脚本: python diagnose_auth_issue.py")
    print("2. 添加会话准备方法到SeleniumHandler")
    print("3. 在数据采集流程中添加API会话准备步骤")
    print("4. 如果仍有问题，尝试替代方案")
    print("5. 使用调试方法获取更多信息")


if __name__ == "__main__":
    main()
