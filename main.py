#!/usr/bin/env python3
"""
服务台数据采集主程序
"""
import sys
import os
import argparse

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.data_collector import ServiceDeskDataCollector


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='服务台数据采集工具')
    parser.add_argument(
        '--config',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='显示详细日志'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建数据采集器
    collector = ServiceDeskDataCollector(config_path=args.config)

    # 运行采集流程
    success = collector.run()

    if success:
        print("✅ 数据采集完成！")
        print("📁 请查看 data/output 目录中的结果文件")
        sys.exit(0)
    else:
        print("❌ 数据采集失败！")
        print("📋 请查看 logs/servicedesk_collector.log 了解详情")
        sys.exit(1)


if __name__ == "__main__":
    main()
