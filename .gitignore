# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 敏感配置文件
config.yaml
config.local.yaml
.env.local

# 数据和输出文件
data/
output/
logs/
*.log

# Chrome用户数据
chrome_user_data/
chrome_data/

# Selenium
geckodriver.log
chromedriver.log
drivers/

# 临时文件
*.tmp
*.temp
test_*/

# 系统文件
.DS_Store
Thumbs.db

# 备份文件
*.bak
*.backup
