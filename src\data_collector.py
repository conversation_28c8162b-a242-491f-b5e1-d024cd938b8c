"""
数据采集主要逻辑
"""
import os
import json
import csv
import logging
from datetime import datetime
from typing import List
import yaml

from .models import Config, ServiceDeskData, Issue
from .selenium_handler import SeleniumHandler
from .api_client import ServiceDeskAPIClient


class ServiceDeskDataCollector:
    """服务台数据采集器"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = None
        self.selenium_handler = None
        self.api_client = None
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        # 确保日志目录存在
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'servicedesk_collector.log')),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)

    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)

            self.config = Config(config_dict)
            self.logger.info("配置文件加载成功")
            return True

        except FileNotFoundError:
            self.logger.error(f"配置文件不存在: {self.config_path}")
            return False
        except yaml.YAMLError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False

    def initialize_selenium(self) -> bool:
        """初始化Selenium"""
        try:
            self.selenium_handler = SeleniumHandler(self.config)
            return self.selenium_handler.setup_driver()
        except Exception as e:
            self.logger.error(f"初始化Selenium失败: {e}")
            return False

    def login(self) -> bool:
        """执行登录"""
        if not self.selenium_handler:
            self.logger.error("Selenium未初始化")
            return False

        return self.selenium_handler.login()

    def initialize_api_client(self) -> bool:
        """初始化API客户端"""
        try:
            if not self.selenium_handler or not self.selenium_handler.driver:
                self.logger.error("Selenium未初始化或WebDriver不可用")
                return False

            api_base_url = self.config.urls.get('api_base')

            # 使用Selenium WebDriver初始化API客户端
            self.api_client = ServiceDeskAPIClient(self.selenium_handler.driver, api_base_url)
            self.logger.info("API客户端初始化成功（基于Selenium）")
            return True

        except Exception as e:
            self.logger.error(f"初始化API客户端失败: {e}")
            return False

    def collect_data(self) -> ServiceDeskData:
        """采集所有数据"""
        if not self.api_client:
            self.logger.error("API客户端未初始化")
            return None

        try:
            # 获取所有请求
            self.logger.info("开始采集服务台数据...")
            requests_data = self.api_client.get_all_requests()

            issues = []
            for request_data in requests_data:
                try:
                    issue = self.api_client.build_issue_from_data(request_data)
                    issues.append(issue)
                    self.logger.info(f"处理Issue: {issue.issue_id}")
                except Exception as e:
                    self.logger.error(f"处理Issue失败: {e}")
                    continue

            # 创建数据集合
            service_desk_data = ServiceDeskData(
                issues=issues,
                total_count=len(issues),
                collection_time=datetime.now().isoformat(),
                domain=self.config.domain
            )

            self.logger.info(f"数据采集完成，共采集 {len(issues)} 个Issue")
            return service_desk_data

        except Exception as e:
            self.logger.error(f"数据采集失败: {e}")
            return None

    def save_data(self, data: ServiceDeskData) -> bool:
        """保存数据"""
        if not data:
            self.logger.error("没有数据需要保存")
            return False

        try:
            # 创建输出目录
            output_dir = self.config.data_collection.get('output_dir', 'output')
            os.makedirs(output_dir, exist_ok=True)

            # 生成文件名时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存JSON格式
            if 'json' in self.config.data_collection.get('formats', ['json']):
                json_file = os.path.join(output_dir, f"servicedesk_data_{timestamp}.json")
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data.to_dict(), f, ensure_ascii=False, indent=2)
                self.logger.info(f"JSON数据已保存: {json_file}")

            # 保存CSV格式
            if 'csv' in self.config.data_collection.get('formats', []):
                self._save_csv_data(data, output_dir, timestamp)

            # 下载附件
            if self.config.data_collection.get('download_attachments', False):
                self._download_attachments(data)

            return True

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False

    def _save_csv_data(self, data: ServiceDeskData, output_dir: str, timestamp: str):
        """保存CSV格式数据"""
        # 保存Issues CSV
        issues_csv = os.path.join(output_dir, f"issues_{timestamp}.csv")
        with open(issues_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Issue ID', 'Self URL', 'Attachments Count', 'Comments Count'])

            for issue in data.issues:
                writer.writerow([
                    issue.issue_id,
                    issue.self_url,
                    len(issue.attachments),
                    len(issue.comments)
                ])

        self.logger.info(f"Issues CSV已保存: {issues_csv}")

        # 保存Comments CSV
        comments_csv = os.path.join(output_dir, f"comments_{timestamp}.csv")
        with open(comments_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Comment ID', 'Issue ID', 'Author', 'Body', 'Created Time'])

            for issue in data.issues:
                for comment in issue.comments:
                    writer.writerow([
                        comment.id,
                        comment.issue_id,
                        comment.author_name,
                        comment.body,
                        comment.created_time
                    ])

        self.logger.info(f"Comments CSV已保存: {comments_csv}")

    def _download_attachments(self, data: ServiceDeskData):
        """下载附件"""
        attachments_dir = self.config.data_collection.get('attachments_dir', 'output/attachments')
        os.makedirs(attachments_dir, exist_ok=True)

        for issue in data.issues:
            for attachment in issue.attachments:
                if attachment.content_url and attachment.filename:
                    file_path = os.path.join(
                        attachments_dir,
                        f"{issue.issue_id}_{attachment.filename}"
                    )
                    self.api_client.download_attachment(
                        attachment.content_url,
                        attachment.filename,
                        file_path
                    )

    def run(self) -> bool:
        """运行完整的数据采集流程"""
        try:
            # 1. 加载配置
            if not self.load_config():
                return False

            # 2. 初始化Selenium
            if not self.initialize_selenium():
                return False

            # 3. 执行登录
            if not self.login():
                return False

            # 4. 准备API会话
            if not self.selenium_handler.prepare_api_session():
                self.logger.error("API会话准备失败，但继续尝试数据采集")
                # 不直接返回False，而是继续尝试

            # 5. 初始化API客户端
            if not self.initialize_api_client():
                return False

            # 6. 采集数据
            data = self.collect_data()
            if not data:
                return False

            # 7. 保存数据
            if not self.save_data(data):
                return False

            self.logger.info("数据采集流程完成")
            return True

        except Exception as e:
            self.logger.error(f"数据采集流程失败: {e}")
            return False

        finally:
            # 清理资源
            if self.selenium_handler:
                self.selenium_handler.close()
