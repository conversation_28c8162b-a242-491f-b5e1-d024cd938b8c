#!/usr/bin/env python3
"""
路径修复验证脚本
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.utils import get_project_root, get_config_path, get_data_dir, get_output_dir, get_logs_dir


def test_project_structure():
    """测试项目结构"""
    print("🏗️ 测试项目结构...")
    
    # 测试项目根目录
    root = get_project_root()
    print(f"   📁 项目根目录: {root}")
    
    # 测试关键目录是否存在
    key_dirs = {
        'src': os.path.join(root, 'src'),
        'tests': os.path.join(root, 'tests'),
        'scripts': os.path.join(root, 'scripts'),
        'docs': os.path.join(root, 'docs'),
    }
    
    for name, path in key_dirs.items():
        if os.path.exists(path):
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (不存在)")
    
    # 测试关键文件是否存在
    key_files = {
        'main.py': os.path.join(root, 'main.py'),
        'requirements.txt': os.path.join(root, 'requirements.txt'),
        'config.template.yaml': os.path.join(root, 'config.template.yaml'),
    }
    
    for name, path in key_files.items():
        if os.path.exists(path):
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (不存在)")


def test_import_paths():
    """测试导入路径"""
    print("\n📦 测试导入路径...")
    
    try:
        from src.models import Config
        print("   ✅ src.models 导入成功")
    except ImportError as e:
        print(f"   ❌ src.models 导入失败: {e}")
    
    try:
        from src.selenium_handler import SeleniumHandler
        print("   ✅ src.selenium_handler 导入成功")
    except ImportError as e:
        print(f"   ❌ src.selenium_handler 导入失败: {e}")
    
    try:
        from src.api_client import ServiceDeskAPIClient
        print("   ✅ src.api_client 导入成功")
    except ImportError as e:
        print(f"   ❌ src.api_client 导入失败: {e}")
    
    try:
        from src.data_collector import ServiceDeskDataCollector
        print("   ✅ src.data_collector 导入成功")
    except ImportError as e:
        print(f"   ❌ src.data_collector 导入失败: {e}")


def test_config_paths():
    """测试配置文件路径"""
    print("\n⚙️ 测试配置文件路径...")
    
    config_path = get_config_path()
    print(f"   📄 配置文件路径: {config_path}")
    
    if os.path.exists(config_path):
        print("   ✅ 配置文件存在")
        
        # 测试配置文件加载
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            from src.models import Config
            config = Config(config_dict)
            print("   ✅ 配置文件格式正确")
            
        except Exception as e:
            print(f"   ❌ 配置文件加载失败: {e}")
    else:
        print("   ⚠️  配置文件不存在，请复制 config.template.yaml 为 config.yaml")


def test_data_directories():
    """测试数据目录"""
    print("\n📁 测试数据目录...")
    
    data_dir = get_data_dir()
    output_dir = get_output_dir()
    logs_dir = get_logs_dir()
    
    print(f"   📁 数据目录: {data_dir}")
    print(f"   📁 输出目录: {output_dir}")
    print(f"   📁 日志目录: {logs_dir}")
    
    # 创建目录（如果不存在）
    from src.utils import ensure_dir_exists
    
    try:
        ensure_dir_exists(data_dir)
        ensure_dir_exists(output_dir)
        ensure_dir_exists(logs_dir)
        ensure_dir_exists(os.path.join(output_dir, 'attachments'))
        
        print("   ✅ 所有数据目录已创建")
    except Exception as e:
        print(f"   ❌ 创建数据目录失败: {e}")


def test_script_execution():
    """测试脚本执行"""
    print("\n🔧 测试脚本执行...")
    
    scripts_to_test = [
        'tests/test_config.py',
        'scripts/simple_demo.py',
    ]
    
    for script in scripts_to_test:
        script_path = os.path.join(project_root, script)
        if os.path.exists(script_path):
            print(f"   📜 {script}: 存在")
            
            # 可以在这里添加实际的执行测试
            # 但为了避免副作用，这里只检查文件存在性
            
        else:
            print(f"   ❌ {script}: 不存在")


def test_relative_imports():
    """测试相对导入"""
    print("\n🔗 测试相对导入...")
    
    # 测试从不同目录运行脚本时的导入
    original_cwd = os.getcwd()
    
    try:
        # 从tests目录运行
        os.chdir(os.path.join(project_root, 'tests'))
        print(f"   📍 切换到tests目录: {os.getcwd()}")
        
        # 重新导入模块测试
        import importlib
        import sys
        
        # 清除已导入的模块
        modules_to_reload = [name for name in sys.modules.keys() if name.startswith('src.')]
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        # 重新设置路径
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        try:
            from src.models import Config
            print("   ✅ 从tests目录导入成功")
        except ImportError as e:
            print(f"   ❌ 从tests目录导入失败: {e}")
        
        # 从scripts目录运行
        os.chdir(os.path.join(project_root, 'scripts'))
        print(f"   📍 切换到scripts目录: {os.getcwd()}")
        
        try:
            from src.models import Config
            print("   ✅ 从scripts目录导入成功")
        except ImportError as e:
            print(f"   ❌ 从scripts目录导入失败: {e}")
        
    finally:
        os.chdir(original_cwd)
        print(f"   📍 恢复原始目录: {os.getcwd()}")


def main():
    """主函数"""
    print("🚀 路径修复验证工具")
    print("=" * 50)
    
    # 运行所有测试
    test_project_structure()
    test_import_paths()
    test_config_paths()
    test_data_directories()
    test_script_execution()
    test_relative_imports()
    
    print("\n✅ 路径验证完成！")
    print("\n📋 如果所有测试都通过，说明路径修复成功")
    print("📋 如果有测试失败，请检查相应的路径配置")


if __name__ == "__main__":
    main()
