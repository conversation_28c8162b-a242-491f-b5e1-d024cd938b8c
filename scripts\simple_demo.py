#!/usr/bin/env python3
"""
简化演示脚本 - 不依赖外部包
"""
import sys
import os
import yaml

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config


def simple_demo():
    """简化演示"""
    print("🎯 服务台数据采集工具 - 简化演示")
    print("=" * 50)

    try:
        # 1. 加载配置
        print("1️⃣ 加载配置文件...")
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)
        print("✅ 配置加载成功")

        # 2. 显示配置信息
        print("\n2️⃣ 配置信息:")
        print(f"   📧 邮箱: {config.email}")
        print(f"   🏢 组织: {config.domain}")
        print(f"   🌐 门户URL: {config.urls.get('portal')}")
        print(f"   🔗 API基础URL: {config.urls.get('api_base')}")
        print(f"   📡 请求API: {config.urls.get('requests_api')}")

        # 3. 检查配置
        print("\n3️⃣ 配置检查:")
        warnings = []

        if not config.email or config.email == "<EMAIL>":
            warnings.append("请设置正确的邮箱地址")

        if not config.password or config.password == "your-password":
            warnings.append("请设置正确的密码")

        if config.domain == "your-organization":
            warnings.append("请设置正确的组织域名")

        if warnings:
            print("⚠️  配置警告:")
            for warning in warnings:
                print(f"   - {warning}")
        else:
            print("✅ 配置检查通过")

        # 4. 显示数据采集流程
        print("\n4️⃣ 数据采集流程:")
        steps = [
            "🔧 初始化Chrome浏览器",
            "🌐 访问服务台门户页面",
            "📝 输入邮箱地址",
            "👆 点击下一步按钮",
            "🔒 输入密码",
            "🚀 点击Continue登录",
            "✅验证登录成功（查找'Welcome to the Help Center'）",
            "🍪 获取登录会话cookies",
            "📡 调用API: /rest/servicedeskapi/request/",
            "📋 解析所有服务请求列表",
            "🔄 对每个请求执行:",
            "   📎 获取附件: {self_url}/attachment",
            "   💬 获取回复: {self_url}/comment",
            "💾 保存数据到JSON格式",
            "📊 保存数据到CSV格式",
            "📁 下载附件文件（可选）",
            "🧹 清理浏览器资源"
        ]

        for i, step in enumerate(steps, 1):
            print(f"   {i:2d}. {step}")

        # 5. 显示输出结构
        print("\n5️⃣ 输出文件结构:")
        print("   📁 output/")
        print("   ├── 📄 servicedesk_data_YYYYMMDD_HHMMSS.json")
        print("   ├── 📄 issues_YYYYMMDD_HHMMSS.csv")
        print("   ├── 📄 comments_YYYYMMDD_HHMMSS.csv")
        print("   └── 📁 attachments/")
        print("       └── 📎 {issue_id}_{filename}")

        # 6. 显示数据结构
        print("\n6️⃣ 数据结构示例:")
        print("   Issue (服务请求):")
        print("   ├── issue_id: 唯一标识符")
        print("   ├── self_url: API链接")
        print("   ├── attachments: 附件列表")
        print("   │   ├── filename: 文件名")
        print("   │   └── content_url: 下载链接")
        print("   └── comments: 回复列表")
        print("       ├── id: 回复ID")
        print("       ├── body: 回复内容")
        print("       ├── author_name: 回复人")
        print("       └── created_time: 创建时间")

        print("\n✅ 演示完成！")

        if warnings:
            print("\n📝 下一步:")
            print("1. 编辑 config.yaml 文件，完善配置")
            print("2. 安装依赖: pip install selenium webdriver-manager requests pyyaml")
            print("3. 运行: python main.py")
        else:
            print("\n🚀 准备就绪！运行以下命令开始采集:")
            print("   pip install selenium webdriver-manager requests pyyaml")
            print("   python main.py")

        return True

    except FileNotFoundError:
        print("❌ 配置文件 config.yaml 不存在")
        print("💡 请复制 config.example.yaml 为 config.yaml 并修改配置")
        return False
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False


if __name__ == "__main__":
    simple_demo()
