# Chrome路径配置指南

## 路径格式要求

在Python和YAML配置文件中，Windows路径需要使用正确的格式来避免转义字符问题。

### ✅ 正确的路径格式

#### 方法1: 使用正斜杠 (推荐)
```yaml
chrome_paths:
  executable_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"
  user_data_dir: "C:/Users/<USER>/ChromeData"
  download_dir: "C:/Users/<USER>/Downloads"
  driver_path: "C:/WebDrivers/chromedriver.exe"
```

#### 方法2: 使用双反斜杠
```yaml
chrome_paths:
  executable_path: "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
  user_data_dir: "C:\\Users\\<USER>\\ChromeData"
  download_dir: "C:\\Users\\<USER>\\Downloads"
  driver_path: "C:\\WebDrivers\\chromedriver.exe"
```

#### 方法3: 使用原始字符串 (在Python代码中)
```python
path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
```

### ❌ 错误的路径格式

```yaml
# 错误：单反斜杠会被解释为转义字符
driver_path: "C:\Program Files\Google\Chrome\Application\chromedriver.exe"
```

## 常见Chrome路径配置

### 1. Chrome可执行文件路径

#### Windows常见位置：
```yaml
# 64位系统
executable_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"

# 32位系统或32位Chrome
executable_path: "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe"

# 用户安装版本
executable_path: "C:/Users/<USER>/AppData/Local/Google/Chrome/Application/chrome.exe"
```

### 2. ChromeDriver路径

#### 自定义ChromeDriver位置：
```yaml
# 放在项目目录
driver_path: "./drivers/chromedriver.exe"

# 放在系统目录
driver_path: "C:/WebDrivers/chromedriver.exe"

# 留空则自动下载
driver_path: ""
```

### 3. 用户数据目录

#### 自定义用户数据目录：
```yaml
# 项目目录下
user_data_dir: "./chrome_data"

# 指定系统目录
user_data_dir: "C:/ChromeUserData"

# 临时目录（推荐用于自动化）
user_data_dir: "C:/temp/chrome_automation"
```

### 4. 下载目录

#### 自定义下载目录：
```yaml
# 项目下载目录
download_dir: "./downloads"

# 系统下载目录
download_dir: "C:/Users/<USER>/Downloads"

# 项目输出目录
download_dir: "./output/downloads"
```

## 完整配置示例

### 基础配置（推荐）
```yaml
selenium:
  browser: "chrome"
  headless: false
  
  chrome_paths:
    executable_path: ""        # 留空自动检测
    user_data_dir: ""         # 留空使用临时目录
    download_dir: "./output/downloads"  # 下载到项目目录
    driver_path: ""           # 留空自动下载
```

### 完全自定义配置
```yaml
selenium:
  browser: "chrome"
  headless: false
  
  chrome_paths:
    executable_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"
    user_data_dir: "C:/ChromeAutomation/UserData"
    download_dir: "C:/ChromeAutomation/Downloads"
    driver_path: "C:/ChromeAutomation/chromedriver.exe"
```

### 便携式配置
```yaml
selenium:
  browser: "chrome"
  headless: false
  
  chrome_paths:
    executable_path: "./portable_chrome/chrome.exe"
    user_data_dir: "./chrome_data"
    download_dir: "./downloads"
    driver_path: "./drivers/chromedriver.exe"
```

## 路径验证

### 检查Chrome安装路径
```python
import os

# 常见Chrome路径
chrome_paths = [
    "C:/Program Files/Google/Chrome/Application/chrome.exe",
    "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
    os.path.expanduser("~/AppData/Local/Google/Chrome/Application/chrome.exe")
]

for path in chrome_paths:
    if os.path.exists(path):
        print(f"找到Chrome: {path}")
```

### 创建目录
```python
import os

# 创建用户数据目录
user_data_dir = "C:/ChromeAutomation/UserData"
os.makedirs(user_data_dir, exist_ok=True)

# 创建下载目录
download_dir = "./output/downloads"
os.makedirs(download_dir, exist_ok=True)
```

## 注意事项

1. **权限问题**: 确保Python有权限访问指定的目录
2. **目录存在**: 用户数据目录和下载目录会自动创建，但父目录必须存在
3. **路径长度**: Windows路径长度限制为260字符
4. **特殊字符**: 避免在路径中使用特殊字符
5. **相对路径**: 相对路径是相对于Python脚本的执行目录

## 故障排除

### 常见错误及解决方案

#### 1. 路径不存在错误
```
FileNotFoundError: [Errno 2] No such file or directory
```
**解决方案**: 检查路径是否正确，使用绝对路径

#### 2. 权限错误
```
PermissionError: [Errno 13] Permission denied
```
**解决方案**: 以管理员身份运行或更改目录权限

#### 3. 转义字符错误
```
SyntaxError: (unicode error) 'unicodeescape' codec can't decode
```
**解决方案**: 使用正斜杠或双反斜杠

## 推荐配置

对于大多数用户，推荐使用以下配置：

```yaml
selenium:
  chrome_paths:
    executable_path: ""                    # 自动检测
    user_data_dir: "./chrome_user_data"    # 项目目录
    download_dir: "./output/downloads"     # 项目下载目录
    driver_path: ""                        # 自动下载
```

这样配置的优点：
- ✅ 便携性好，项目可以在不同机器上运行
- ✅ 不依赖系统Chrome安装位置
- ✅ 下载文件集中管理
- ✅ 自动处理ChromeDriver版本匹配
