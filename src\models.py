"""
数据模型定义
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import json


@dataclass
class Attachment:
    """附件模型"""
    filename: str
    content_url: str
    issue_id: str


@dataclass
class Comment:
    """回复模型"""
    id: str
    body: str
    author_name: str
    created_time: int  # epochMillis
    issue_id: str


@dataclass
class Issue:
    """问题/提问模型"""
    issue_id: str
    self_url: str
    raw_data: Dict[str, Any]  # 原始数据
    attachments: List[Attachment]
    comments: List[Comment]


@dataclass
class ServiceDeskData:
    """服务台数据集合"""
    issues: List[Issue]
    total_count: int
    collection_time: str
    domain: str

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'self_url': issue.self_url,
                    'raw_data': issue.raw_data,
                    'attachments': [
                        {
                            'filename': att.filename,
                            'content_url': att.content_url,
                            'issue_id': att.issue_id
                        } for att in issue.attachments
                    ],
                    'comments': [
                        {
                            'id': comment.id,
                            'body': comment.body,
                            'author_name': comment.author_name,
                            'created_time': comment.created_time,
                            'issue_id': comment.issue_id
                        } for comment in issue.comments
                    ]
                } for issue in self.issues
            ],
            'total_count': self.total_count,
            'collection_time': self.collection_time,
            'domain': self.domain
        }


@dataclass
class Config:
    """配置模型"""
    def __init__(self, config_dict: Dict[str, Any]):
        self.servicedesk = config_dict.get('servicedesk', {})
        self.selenium = config_dict.get('selenium', {})
        self.data_collection = config_dict.get('data_collection', {})

        # 构建完整的URL
        domain = self.servicedesk.get('base_domain', 'tuduweb')
        urls = self.servicedesk.get('urls', {})
        self.urls = {}
        for key, url_template in urls.items():
            self.urls[key] = url_template.format(domain=domain)

    @property
    def email(self) -> str:
        return self.servicedesk.get('login', {}).get('email', '')

    @property
    def password(self) -> str:
        return self.servicedesk.get('login', {}).get('password', '')

    @property
    def domain(self) -> str:
        return self.servicedesk.get('base_domain', 'tuduweb')
