#!/usr/bin/env python3
"""
配置测试脚本
"""
import yaml
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config


def test_config(config_path="config.yaml"):
    """测试配置文件"""
    try:
        # 确保使用项目根目录的配置文件
        if not os.path.isabs(config_path):
            config_path = os.path.join(project_root, config_path)

        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)

        print("✅ 配置文件加载成功")
        print(f"📧 邮箱: {config.email}")
        print(f"🏢 组织: {config.domain}")
        print(f"🌐 门户URL: {config.urls.get('portal')}")
        print(f"🔗 API URL: {config.urls.get('api_base')}")

        # 检查必要配置
        if not config.email or config.email == "<EMAIL>":
            print("⚠️  警告: 请在配置文件中设置正确的邮箱地址")

        if not config.password or config.password == "your-password":
            print("⚠️  警告: 请在配置文件中设置正确的密码")

        if config.domain == "your-organization":
            print("⚠️  警告: 请在配置文件中设置正确的组织域名")

        print("\n✅ 配置检查完成")
        return True

    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_path}")
        print("💡 请复制 config.example.yaml 为 config.yaml 并修改配置")
        return False
    except yaml.YAMLError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


if __name__ == "__main__":
    config_file = sys.argv[1] if len(sys.argv) > 1 else "config.yaml"
    test_config(config_file)
