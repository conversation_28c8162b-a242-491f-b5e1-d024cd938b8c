"""
工具函数模块
"""
import os
import sys


def get_project_root():
    """获取项目根目录"""
    # 从当前文件位置向上查找项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)  # src的上级目录
    return project_root


def setup_project_path():
    """设置项目路径到Python路径"""
    project_root = get_project_root()
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    return project_root


def get_config_path(config_filename="config.yaml"):
    """获取配置文件的完整路径"""
    project_root = get_project_root()
    return os.path.join(project_root, config_filename)


def get_data_dir():
    """获取数据目录路径"""
    project_root = get_project_root()
    return os.path.join(project_root, "data")


def get_output_dir():
    """获取输出目录路径"""
    data_dir = get_data_dir()
    return os.path.join(data_dir, "output")


def get_logs_dir():
    """获取日志目录路径"""
    project_root = get_project_root()
    return os.path.join(project_root, "logs")


def ensure_dir_exists(directory):
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)
    return directory


def get_relative_path_from_script(script_file, target_file):
    """从脚本文件获取目标文件的相对路径"""
    script_dir = os.path.dirname(os.path.abspath(script_file))
    project_root = get_project_root()
    
    # 如果目标文件是相对路径，则相对于项目根目录
    if not os.path.isabs(target_file):
        target_file = os.path.join(project_root, target_file)
    
    return target_file
