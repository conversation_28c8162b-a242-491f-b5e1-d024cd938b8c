# 服务台数据采集项目总结

## 项目概述

本项目是一个基于Selenium和API的Atlassian服务台数据采集工具，能够自动登录并采集所有的服务请求、附件和回复数据。

## 核心功能

### 🔐 自动登录流程
1. 访问服务台门户页面
2. 输入邮箱地址（Input id=user-email）
3. 点击下一步（Next id=login-button）
4. 输入密码（Input id=user-password）
5. 点击Continue登录（id=login-button）
6. 验证登录成功（查找"Welcome to the Help Center"文本）

### 📊 数据采集功能（基于Selenium浏览器）
1. **获取所有服务请求**: 通过浏览器访问 `/rest/servicedeskapi/request/`
2. **提取Issue信息**:
   - `values.issueId` - 唯一标识符
   - `values._links.self` - API链接地址
3. **获取附件信息**: 通过浏览器访问 `{self_url}/attachment`
   - `values.filename` - 附件名称
   - `values._links.content` - 附件下载链接
4. **获取回复信息**: 通过浏览器访问 `{self_url}/comment`
   - `values.id` - 回复ID
   - `values.body` - 回复内容
   - `values.author.displayName` - 回复人姓名
   - `values.created.epochMillis` - 回复时间

**技术特点**:
- 使用 `driver.get(api_url)` 访问API地址
- 从页面源码中提取JSON数据
- 自动继承浏览器登录会话
- 无需额外的API认证处理

## 项目结构

```
selenium-servicedesk/
├── 📄 README.md                 # 项目说明文档
├── 📄 PROJECT_SUMMARY.md        # 项目总结（本文件）
├── 📄 requirements.txt          # Python依赖包
├── 📄 config.yaml              # 配置文件（需要用户配置）
├── 📄 config.example.yaml      # 配置文件示例
├── 📄 .gitignore               # Git忽略文件
├── 📄 main.py                  # 主程序入口
├── 📄 setup.py                 # 项目设置脚本
├── 📄 test_config.py           # 配置测试脚本
├── 📄 demo.py                  # 完整演示脚本
├── 📄 simple_demo.py           # 简化演示脚本
└── 📁 src/                     # 源代码目录
    ├── 📄 __init__.py          # 包初始化
    ├── 📄 models.py            # 数据模型定义
    ├── 📄 selenium_handler.py  # Selenium自动化操作
    ├── 📄 api_client.py        # API客户端
    └── 📄 data_collector.py    # 主要采集逻辑
```

## 核心模块说明

### 1. models.py - 数据模型
- `Attachment`: 附件模型
- `Comment`: 回复模型
- `Issue`: 服务请求模型
- `ServiceDeskData`: 数据集合模型
- `Config`: 配置模型

### 2. selenium_handler.py - Selenium操作
- `SeleniumHandler`: 浏览器自动化操作类
- 支持Chrome浏览器（可配置无头模式）
- 自动下载ChromeDriver
- 完整的登录流程实现

### 3. api_client.py - API客户端
- `ServiceDeskAPIClient`: API调用客户端
- 使用登录会话cookies进行API调用
- 支持获取请求、附件、回复数据
- 支持附件下载功能

### 4. data_collector.py - 主采集逻辑
- `ServiceDeskDataCollector`: 数据采集器主类
- 整合Selenium登录和API数据采集
- 支持多种输出格式（JSON、CSV）
- 完整的错误处理和日志记录

## 配置说明

### 必需配置项
```yaml
servicedesk:
  base_domain: "your-organization"  # 组织域名
  login:
    email: "<EMAIL>"     # 登录邮箱
    password: "your-password"           # 登录密码
```

### 可选配置项
- 浏览器设置（Chrome、无头模式等）
- 超时时间配置
- 输出格式选择
- 附件下载开关

## 使用方法

### 1. 基础设置
```bash
# 1. 复制配置文件
cp config.example.yaml config.yaml

# 2. 编辑配置文件（设置邮箱、密码、组织域名）
# 3. 安装依赖
pip install selenium webdriver-manager requests pyyaml
```

### 2. 运行命令
```bash
# 测试配置
python test_config.py

# 查看演示
python simple_demo.py

# 开始数据采集
python main.py
```

## 输出文件

### JSON格式
- `servicedesk_data_YYYYMMDD_HHMMSS.json`: 完整的结构化数据

### CSV格式
- `issues_YYYYMMDD_HHMMSS.csv`: Issues列表
- `comments_YYYYMMDD_HHMMSS.csv`: 所有回复数据

### 附件文件
- `output/attachments/{issue_id}_{filename}`: 下载的附件文件

## 技术特点

### ✅ 优势
1. **完全自动化**: 无需手动操作，全程自动化采集
2. **灵活配置**: 支持多组织、多账号配置
3. **多格式输出**: JSON和CSV双格式支持
4. **完整数据**: 包含请求、附件、回复的完整信息
5. **错误处理**: 完善的异常处理和日志记录
6. **可扩展性**: 模块化设计，易于扩展功能

### ⚠️ 注意事项
1. 需要有效的服务台访问权限
2. 不支持双因素认证账号
3. 大量数据采集可能需要较长时间
4. 需要稳定的网络连接

## 依赖包

- `selenium`: Web自动化框架（用于登录和访问API）
- `webdriver-manager`: 自动管理WebDriver
- `pyyaml`: YAML配置文件解析

**注意**: 不再需要 `requests` 库，所有HTTP请求都通过Selenium浏览器完成

## 安全考虑

1. 配置文件包含敏感信息，已加入.gitignore
2. 建议使用专用账号进行数据采集
3. 遵守相关使用条款和隐私政策
4. 建议在非高峰时段运行

## 扩展可能

1. 支持更多浏览器（Firefox、Edge等）
2. 添加数据过滤和筛选功能
3. 支持增量采集
4. 添加数据分析和报告功能
5. 支持多线程并发采集

## 项目状态

✅ **已完成功能**:
- 基础项目结构
- 配置管理系统
- Selenium自动化登录
- API数据采集
- 多格式数据输出
- 附件下载功能
- 错误处理和日志
- 完整文档和演示

🔄 **待测试功能**:
- 实际登录流程测试
- API数据采集测试
- 大量数据处理测试

本项目提供了一个完整的服务台数据采集解决方案，可以根据具体需求进行定制和扩展。
