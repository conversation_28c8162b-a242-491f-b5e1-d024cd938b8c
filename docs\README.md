# 服务台数据采集工具

基于Selenium浏览器的Atlassian服务台数据采集工具，通过浏览器自动登录并访问API地址来采集所有的服务请求、附件和回复数据。

## 功能特性

- 🔐 **自动登录**: 使用Selenium自动化登录Atlassian服务台
- 📊 **数据采集**: 通过Selenium浏览器访问API地址获取所有服务请求、附件和回复
- 💾 **多格式输出**: 支持JSON和CSV格式数据导出
- 📎 **附件下载**: 可选择下载所有附件文件
- ⚙️ **灵活配置**: 支持多组织配置，账号密码可配置
- 📝 **详细日志**: 完整的操作日志记录
- 🌐 **纯浏览器方案**: 完全基于Selenium，无需额外的API认证

## 项目结构

```
selenium-servicedesk/
├── 📁 src/                  # 源代码目录
├── 📁 tests/                # 测试文件
├── 📁 scripts/              # 工具脚本
├── 📁 docs/                 # 文档目录
├── 📁 data/                 # 数据目录（被git忽略）
├── 📁 logs/                 # 日志目录（被git忽略）
├── 📄 main.py               # 主程序入口
├── 📄 requirements.txt      # Python依赖
├── 📄 config.template.yaml  # 配置文件模板
└── 📄 .gitignore           # Git忽略文件
```

详细结构说明请查看：[项目结构文档](PROJECT_STRUCTURE.md)

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置文件

复制配置模板并编辑：

```bash
# 复制配置模板
cp config.template.yaml config.yaml

# 编辑配置文件，填入真实信息
nano config.yaml  # 或使用其他编辑器
```

修改以下关键配置：

```yaml
servicedesk:
  base_domain: "your-org"  # 修改为你的组织名
  login:
    email: "<EMAIL>"     # 修改为你的邮箱
    password: "your-password"           # 修改为你的密码
```

**注意**: `config.yaml` 包含敏感信息，已被git忽略，不会上传到版本控制。

### 3. Chrome浏览器

确保系统已安装Chrome浏览器，工具会自动下载对应的ChromeDriver。

#### Chrome路径配置（可选）

如果需要自定义Chrome路径，可以在配置文件中设置：

```yaml
selenium:
  chrome_paths:
    # Chrome可执行文件路径（留空则自动检测）
    executable_path: "C:/Program Files/Google/Chrome/Application/chrome.exe"

    # 用户数据目录（留空则使用临时目录）
    user_data_dir: "./chrome_user_data"

    # 下载目录（留空则使用默认目录）
    download_dir: "./output/downloads"

    # ChromeDriver路径（留空则自动下载）
    driver_path: ""
```

**路径格式要求**：
- ✅ 使用正斜杠：`"C:/Program Files/Chrome/chrome.exe"`
- ✅ 使用双反斜杠：`"C:\\Program Files\\Chrome\\chrome.exe"`
- ❌ 避免单反斜杠：`"C:\Program Files\Chrome\chrome.exe"`

## 使用方法

### 基本使用

```bash
python main.py
```

### 指定配置文件

```bash
python main.py --config my_config.yaml
```

### 显示详细日志

```bash
python main.py --verbose
```

### 测试Chrome路径配置

```bash
python tests/test_chrome_paths.py
```

### 测试配置文件

```bash
python tests/test_config.py
```

### 测试Selenium API访问

```bash
python tests/test_selenium_api.py
```

### 项目设置和演示

```bash
# 项目初始化
python scripts/setup.py

# 简化演示
python scripts/simple_demo.py

# 认证问题诊断
python scripts/diagnose_auth_issue.py
```

## 技术实现

### 数据采集方式

本工具采用**纯Selenium浏览器方案**，具有以下特点：

1. **登录阶段**: 使用Selenium自动化登录服务台
2. **数据获取**: 通过`driver.get(api_url)`直接访问API地址
3. **数据解析**: 从页面源码中提取JSON数据
4. **会话继承**: 自动继承浏览器的登录会话状态

### API访问流程

```python
# 1. 访问API地址
driver.get("https://domain.atlassian.net/rest/servicedeskapi/request/")

# 2. 获取页面源码
page_source = driver.page_source

# 3. 提取JSON数据
json_data = extract_json_from_page(page_source)

# 4. 解析数据
issues = json_data.get('values', [])
```

### 优势

- ✅ **无需API认证**: 自动继承浏览器登录状态
- ✅ **简单可靠**: 不需要处理复杂的OAuth或Token
- ✅ **兼容性好**: 支持所有浏览器支持的认证方式
- ✅ **易于调试**: 可以直接在浏览器中查看API响应

## 数据结构

### Issue (服务请求)
- `issue_id`: 唯一标识符
- `self_url`: API链接地址
- `attachments`: 附件列表
- `comments`: 回复列表
- `raw_data`: 原始API数据

### Attachment (附件)
- `filename`: 文件名
- `content_url`: 下载链接
- `issue_id`: 所属Issue ID

### Comment (回复)
- `id`: 回复ID
- `body`: 回复内容
- `author_name`: 回复人姓名
- `created_time`: 创建时间(epochMillis)
- `issue_id`: 所属Issue ID

## 输出文件

### JSON格式
- `servicedesk_data_YYYYMMDD_HHMMSS.json`: 完整的结构化数据

### CSV格式
- `issues_YYYYMMDD_HHMMSS.csv`: Issues列表
- `comments_YYYYMMDD_HHMMSS.csv`: 所有回复数据

### 附件文件
- `output/attachments/{issue_id}_{filename}`: 下载的附件文件

## 配置选项

### Selenium配置
```yaml
selenium:
  browser: "chrome"        # 浏览器类型
  headless: false         # 是否无头模式

  # Chrome路径配置（可选）
  chrome_paths:
    executable_path: ""    # Chrome可执行文件路径
    user_data_dir: ""     # 用户数据目录
    download_dir: ""      # 下载目录
    driver_path: ""       # ChromeDriver路径

  timeouts:
    implicit_wait: 10     # 隐式等待时间
    page_load_timeout: 30 # 页面加载超时
```

### 数据采集配置
```yaml
data_collection:
  output_dir: "output"           # 输出目录
  formats: ["json", "csv"]       # 输出格式
  download_attachments: true     # 是否下载附件
  attachments_dir: "output/attachments"  # 附件目录
```

## 故障排除

### 常见问题

1. **登录失败**
   - 检查邮箱和密码是否正确
   - 确认账号没有启用双因素认证
   - 检查网络连接

2. **Chrome浏览器问题**
   - 确保Chrome浏览器已安装
   - 尝试更新Chrome到最新版本

3. **API访问失败**
   - 确认登录成功
   - 检查组织域名配置是否正确

### 日志文件

查看 `servicedesk_collector.log` 文件获取详细的错误信息。

## 注意事项

- 请确保有合法的服务台访问权限
- 大量数据采集可能需要较长时间
- 建议在非高峰时段运行
- 请遵守相关的使用条款和隐私政策

## 许可证

本项目仅供学习和合法的数据采集使用。
