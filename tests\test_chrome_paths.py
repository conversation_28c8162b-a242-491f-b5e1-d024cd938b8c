#!/usr/bin/env python3
"""
Chrome路径配置测试脚本
"""
import os
import sys
import yaml

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config


def find_chrome_executable():
    """查找Chrome可执行文件"""
    print("🔍 查找Chrome可执行文件...")

    # Windows常见Chrome路径
    chrome_paths = [
        "C:/Program Files/Google/Chrome/Application/chrome.exe",
        "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
        os.path.expanduser("~/AppData/Local/Google/Chrome/Application/chrome.exe"),
        os.path.expanduser("~/AppData/Local/Chromium/Application/chrome.exe"),
    ]

    found_paths = []
    for path in chrome_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"  ✅ 找到: {path}")

    if not found_paths:
        print("  ❌ 未找到Chrome可执行文件")
        print("  💡 请手动指定Chrome路径")

    return found_paths


def test_path_formats():
    """测试不同的路径格式"""
    print("\n🧪 测试路径格式...")

    test_paths = [
        # 正确格式
        "C:/Program Files/Google/Chrome/Application/chrome.exe",
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "./chrome_data",
        "~/Downloads",

        # 错误格式（会被修正）
        "C:\Program Files\Google\Chrome\Application\chrome.exe",
    ]

    from src.selenium_handler import SeleniumHandler
    from src.models import Config

    # 创建一个临时的SeleniumHandler来测试路径标准化
    config = Config({})
    handler = SeleniumHandler(config)

    for path in test_paths:
        try:
            normalized = handler._normalize_path(path)
            print(f"  📁 原路径: {path}")
            print(f"  ✅ 标准化: {normalized}")
            print()
        except Exception as e:
            print(f"  ❌ 错误: {path} -> {e}")


def test_directory_creation():
    """测试目录创建"""
    print("📁 测试目录创建...")

    test_dirs = [
        "./test_chrome_data",
        "./test_downloads",
        "./test_drivers"
    ]

    from src.selenium_handler import SeleniumHandler
    from src.models import Config

    config = Config({})
    handler = SeleniumHandler(config)

    for directory in test_dirs:
        try:
            success = handler._ensure_directory_exists(directory)
            if success and os.path.exists(directory):
                print(f"  ✅ 创建成功: {directory}")
                # 清理测试目录
                os.rmdir(directory)
            else:
                print(f"  ❌ 创建失败: {directory}")
        except Exception as e:
            print(f"  ❌ 错误: {directory} -> {e}")


def generate_config_example():
    """生成配置示例"""
    print("\n📝 生成配置示例...")

    chrome_paths = find_chrome_executable()

    config_example = {
        'selenium': {
            'browser': 'chrome',
            'headless': False,
            'chrome_paths': {
                'executable_path': chrome_paths[0] if chrome_paths else "",
                'user_data_dir': "./chrome_user_data",
                'download_dir': "./output/downloads",
                'driver_path': ""
            },
            'timeouts': {
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'script_timeout': 30
            }
        }
    }

    print("  📋 推荐配置:")
    print(yaml.dump(config_example, default_flow_style=False, allow_unicode=True))


def validate_current_config():
    """验证当前配置"""
    print("🔧 验证当前配置...")

    try:
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)
        chrome_paths = config.selenium.get('chrome_paths', {})

        print("  📋 当前Chrome路径配置:")

        # 检查可执行文件路径
        executable_path = chrome_paths.get('executable_path', '')
        if executable_path:
            if os.path.exists(executable_path):
                print(f"  ✅ Chrome路径: {executable_path}")
            else:
                print(f"  ❌ Chrome路径不存在: {executable_path}")
        else:
            print("  ℹ️  Chrome路径: 使用自动检测")

        # 检查用户数据目录
        user_data_dir = chrome_paths.get('user_data_dir', '')
        if user_data_dir:
            print(f"  📁 用户数据目录: {user_data_dir}")
        else:
            print("  ℹ️  用户数据目录: 使用临时目录")

        # 检查下载目录
        download_dir = chrome_paths.get('download_dir', '')
        if download_dir:
            print(f"  📥 下载目录: {download_dir}")
        else:
            print("  ℹ️  下载目录: 使用默认目录")

        # 检查ChromeDriver路径
        driver_path = chrome_paths.get('driver_path', '')
        if driver_path:
            if os.path.exists(driver_path):
                print(f"  ✅ ChromeDriver路径: {driver_path}")
            else:
                print(f"  ❌ ChromeDriver路径不存在: {driver_path}")
        else:
            print("  ℹ️  ChromeDriver路径: 使用自动下载")

        return True

    except FileNotFoundError:
        print("  ❌ 配置文件 config.yaml 不存在")
        return False
    except Exception as e:
        print(f"  ❌ 配置验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Chrome路径配置测试工具")
    print("=" * 50)

    # 1. 查找Chrome
    find_chrome_executable()

    # 2. 测试路径格式
    test_path_formats()

    # 3. 测试目录创建
    test_directory_creation()

    # 4. 验证当前配置
    validate_current_config()

    # 5. 生成配置示例
    generate_config_example()

    print("\n✅ 测试完成！")
    print("\n📖 更多信息请查看: PATH_CONFIG_GUIDE.md")


if __name__ == "__main__":
    main()
