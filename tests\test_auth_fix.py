#!/usr/bin/env python3
"""
测试认证修复效果
"""
import sys
import os
import yaml

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models import Config
from src.selenium_handler import SeleniumHandler


def test_auth_fix():
    """测试认证修复效果"""
    print("🧪 测试认证修复效果")
    print("=" * 50)

    try:
        # 1. 加载配置
        print("1️⃣ 加载配置...")
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        config = Config(config_dict)
        print(f"   📧 邮箱: {config.email}")
        print(f"   🏢 组织: {config.domain}")

        # 2. 初始化Selenium
        print("\n2️⃣ 初始化Selenium...")
        selenium_handler = SeleniumHandler(config)
        if not selenium_handler.setup_driver():
            print("   ❌ Selenium初始化失败")
            return False
        print("   ✅ Selenium初始化成功")

        # 3. 执行登录
        print("\n3️⃣ 执行登录...")
        if not selenium_handler.login():
            print("   ❌ 登录失败")
            selenium_handler.close()
            return False
        print("   ✅ 登录成功")

        # 4. 测试新的API会话准备功能
        print("\n4️⃣ 测试API会话准备...")
        if selenium_handler.prepare_api_session():
            print("   ✅ API会话准备成功")
        else:
            print("   ⚠️  API会话准备失败，但可能仍能访问某些API")

        # 5. 测试API访问
        print("\n5️⃣ 测试API访问...")
        from src.api_client import ServiceDeskAPIClient

        api_base_url = config.urls.get('api_base')
        api_client = ServiceDeskAPIClient(selenium_handler.driver, api_base_url)

        # 尝试获取服务请求
        requests_data = api_client.get_all_requests()

        if requests_data:
            print(f"   ✅ API访问成功！获取到 {len(requests_data)} 个服务请求")

            # 显示第一个请求的信息
            if requests_data:
                first_request = requests_data[0]
                print(f"   📋 第一个请求ID: {first_request.get('issueId', 'N/A')}")
                print(f"   📝 请求摘要: {first_request.get('summary', 'N/A')[:50]}...")
        else:
            print("   ❌ API访问失败，未获取到数据")

        # 6. 显示会话信息
        print("\n6️⃣ 会话信息...")
        cookies = selenium_handler.get_cookies()
        print(f"   🍪 Cookies数量: {len(cookies)}")

        important_cookies = ['JSESSIONID', 'atlassian.xsrf.token', 'cloud.session.token']
        for cookie_name in important_cookies:
            if cookie_name in cookies:
                print(f"   ✅ {cookie_name}: 存在")
            else:
                print(f"   ⚠️  {cookie_name}: 缺失")

        selenium_handler.close()
        return len(requests_data) > 0 if requests_data else False

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


def test_step_by_step_api_access():
    """逐步测试API访问"""
    print("\n🔬 逐步测试API访问")
    print("=" * 50)

    try:
        # 加载配置
        config_path = os.path.join(project_root, 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        config = Config(config_dict)

        # 初始化Selenium
        selenium_handler = SeleniumHandler(config)
        selenium_handler.setup_driver()

        # 登录
        selenium_handler.login()

        # 测试不同的API端点
        test_endpoints = [
            {
                'name': '用户信息API',
                'url': f"https://{config.domain}.atlassian.net/rest/api/2/myself",
                'success_indicators': ['accountId', 'displayName', 'emailAddress']
            },
            {
                'name': '服务台信息API',
                'url': f"https://{config.domain}.atlassian.net/rest/servicedeskapi/info",
                'success_indicators': ['version']
            },
            {
                'name': '服务台列表API',
                'url': f"https://{config.domain}.atlassian.net/rest/servicedeskapi/servicedesk",
                'success_indicators': ['values']
            },
            {
                'name': '服务请求API',
                'url': f"https://{config.domain}.atlassian.net/rest/servicedeskapi/request/",
                'success_indicators': ['values']
            }
        ]

        successful_endpoints = []

        for endpoint in test_endpoints:
            print(f"\n🔗 测试: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")

            selenium_handler.driver.get(endpoint['url'])
            import time
            time.sleep(2)

            current_url = selenium_handler.driver.current_url
            page_source = selenium_handler.driver.page_source

            print(f"   📍 实际URL: {current_url}")

            if 'login' in current_url.lower():
                print("   ❌ 被重定向到登录页面")
            elif 'Client must be authenticated' in page_source:
                print("   ❌ 认证失败")
            elif any(indicator in page_source for indicator in endpoint['success_indicators']):
                print("   ✅ 访问成功")
                successful_endpoints.append(endpoint['name'])
            else:
                print("   ⚠️  响应异常")
                print(f"   📄 内容预览: {page_source[:100]}...")

        selenium_handler.close()

        print(f"\n📊 测试结果: {len(successful_endpoints)}/{len(test_endpoints)} 个端点成功")
        if successful_endpoints:
            print("✅ 成功的端点:")
            for endpoint in successful_endpoints:
                print(f"   - {endpoint}")

        return len(successful_endpoints) > 0

    except Exception as e:
        print(f"❌ 逐步测试过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    print("🚀 认证修复测试工具")
    print("=" * 50)

    # 测试修复效果
    success1 = test_auth_fix()

    # 逐步测试API访问
    success2 = test_step_by_step_api_access()

    print("\n📋 测试总结:")
    print("=" * 30)

    if success1:
        print("✅ 主要API访问测试: 成功")
    else:
        print("❌ 主要API访问测试: 失败")

    if success2:
        print("✅ 逐步API测试: 至少一个端点成功")
    else:
        print("❌ 逐步API测试: 所有端点都失败")

    if success1 or success2:
        print("\n🎉 认证问题已部分或完全解决！")
        print("💡 建议:")
        print("   - 如果只有部分API成功，可以使用成功的端点")
        print("   - 运行完整的数据采集测试: python main.py")
    else:
        print("\n😞 认证问题仍然存在")
        print("💡 建议:")
        print("   - 检查账号是否有API访问权限")
        print("   - 联系管理员确认API访问策略")
        print("   - 尝试使用不同的认证方式")


if __name__ == "__main__":
    main()
